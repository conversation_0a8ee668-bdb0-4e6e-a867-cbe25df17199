{"version": 3, "file": "LSTool.js", "sourceRoot": "", "sources": ["../../../../src/core/tools/filesystem/LSTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA+B;AAC/B,2CAA6B;AAC7B,+CAAkE;AAGlE,MAAa,MAAO,SAAQ,mBAAQ;IAC3B,IAAI,GAAG,gBAAgB,CAAC;IACxB,WAAW,GAAG,yDAAyD,CAAC;IACxE,oBAAoB,GAAG,KAAK,CAAC;IAE7B,UAAU,GAAG;QAClB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,+CAA+C;aAC7D;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,4CAA4C;gBACzD,OAAO,EAAE,KAAK;aACf;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,8CAA8C;gBAC3D,OAAO,EAAE,KAAK;aACf;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;gBAC1C,WAAW,EAAE,oCAAoC;gBACjD,OAAO,EAAE,MAAM;aAChB;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,qCAAqC;gBAClD,OAAO,EAAE,CAAC;aACX;SACF;QACD,QAAQ,EAAE,CAAC,MAAM,CAAC;KACnB,CAAC;IAEF;QACE,KAAK,CAAC;YACJ,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;YACvC,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,KAAK;YAChB,oBAAoB,EAAE,KAAK;SAC5B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,MAA2B;QAC/C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,+BAA+B;QAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,0BAA0B;QAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACjE,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;QAE3B,+BAA+B;QAC/B,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAChC,MAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAC9D,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC,sCAAsC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE;gBACnE,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,EAAE;gBACP,OAAO,EAAE,IAAI;aACd,CAAC,CAAC,CAAC;QACN,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,sBAAsB,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAK,KAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,IAAI,kBAAkB,CAAC,CAAC;YAC3D,CAAC;iBAAM,IAAK,KAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,gCAAgC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,MAA2B;QAC9C,IAAI,CAAC;YACH,MAAM,EACJ,IAAI,EAAE,OAAO,EACb,SAAS,GAAG,KAAK,EACjB,UAAU,GAAG,KAAK,EAClB,MAAM,GAAG,MAAM,EACf,QAAQ,GAAG,CAAC,GACb,GAAG,MAAM,CAAC;YAEX,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC3C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CACtC,YAAY,EACZ,SAAS,EACT,UAAU,EACV,MAAM,EACN,QAAQ,EACR,CAAC,CACF,CAAC;YAEF,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAE5C,OAAO,IAAI,CAAC,mBAAmB,CAC7B,eAAe,EACf,UAAU,OAAO,CAAC,MAAM,aAAa,OAAO,EAAE,EAC9C;gBACE,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE,OAAO,CAAC,MAAM;gBAC1B,OAAO;aACR,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACtF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,OAAe,EACf,SAAkB,EAClB,UAAmB,EACnB,MAAc,EACd,QAAgB,EAChB,YAAoB;QAEpB,MAAM,OAAO,GAAqB,EAAE,CAAC;QAErC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAExC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,qCAAqC;gBACrC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACxC,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAE1C,IAAI,CAAC;oBACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACtC,MAAM,KAAK,GAAmB;wBAC5B,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,QAAQ;wBACd,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC;wBACpD,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;wBAChD,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,QAAQ,EAAE,KAAK,CAAC,KAAK;wBACrB,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;wBACvC,KAAK,EAAE,YAAY;qBACpB,CAAC;oBAEF,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAEpB,2CAA2C;oBAC3C,IAAI,SAAS,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,YAAY,GAAG,QAAQ,EAAE,CAAC;wBAChE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CACzC,QAAQ,EACR,SAAS,EACT,UAAU,EACV,MAAM,EACN,QAAQ,EACR,YAAY,GAAG,CAAC,CACjB,CAAC;wBACF,OAAO,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;oBAC9B,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,oCAAoC;oBACpC,SAAS;gBACX,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,yBAAyB,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAEO,WAAW,CAAC,OAAyB,EAAE,MAAc;QAC3D,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,MAAM;oBACT,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;gBACzB,KAAK,UAAU;oBACb,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrD,KAAK,MAAM;oBACT,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;wBACtB,OAAO,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzC,CAAC;oBACD,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACtC,KAAK,MAAM,CAAC;gBACZ;oBACE,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,OAAyB,EAAE,QAAgB;QACxE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,aAAa,QAAQ,WAAW,CAAC;QAC1C,CAAC;QAED,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,KAAK,CAAC,IAAI,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;QACjD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEf,wCAAwC;QACxC,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAC5C,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;gBAAE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YAC7C,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7B,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAsC,CAAC,CAAC;QAE3C,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACnF,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAE9C,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC1D,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1E,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBAErD,KAAK,CAAC,IAAI,CACR,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,WAAW,IAAI,QAAQ,EAAE,CAC9E,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,aAAa,CAAC,OAAyB;QAC7C,MAAM,OAAO,GAAG;YACd,UAAU,EAAE,CAAC;YACb,gBAAgB,EAAE,CAAC;YACnB,SAAS,EAAE,CAAC;SACb,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC1B,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,cAAc,CAAC,KAAe;QACpC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,oBAAoB;QACpB,WAAW,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1C,WAAW,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1C,WAAW,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAE1C,oBAAoB;QACpB,WAAW,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1C,WAAW,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1C,WAAW,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAE1C,oBAAoB;QACpB,WAAW,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1C,WAAW,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1C,WAAW,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAE1C,OAAO,WAAW,CAAC;IACrB,CAAC;CACF;AA9SD,wBA8SC"}