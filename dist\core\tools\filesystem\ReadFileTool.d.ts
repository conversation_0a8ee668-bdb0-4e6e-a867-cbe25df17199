import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';
export declare class ReadFileTool extends BaseTool {
    name: string;
    description: string;
    requiresConfirmation: boolean;
    parameters: {
        type: "object";
        properties: {
            file_path: {
                type: string;
                description: string;
            };
            encoding: {
                type: string;
                enum: string[];
                description: string;
                default: string;
            };
            start_line: {
                type: string;
                description: string;
                minimum: number;
            };
            end_line: {
                type: string;
                description: string;
                minimum: number;
            };
            max_size: {
                type: string;
                description: string;
                default: number;
            };
            preview_only: {
                type: string;
                description: string;
                default: boolean;
            };
            preview_lines: {
                type: string;
                description: string;
                default: number;
                minimum: number;
                maximum: number;
            };
        };
        required: string[];
    };
    constructor();
    validate(params: Record<string, any>): Promise<ToolValidationResult>;
    execute(params: Record<string, any>): Promise<ToolResult>;
    private readFilePreview;
    private readFileLines;
    private isBinaryFile;
    private createDisplayContent;
}
//# sourceMappingURL=ReadFileTool.d.ts.map