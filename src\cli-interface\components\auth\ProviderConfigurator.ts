import inquirer from 'inquirer';
import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { SUPPORTED_PROVIDERS } from '../../../core/types';
import { CLIState, CLIConfig } from '../../types';

export class ProviderConfigurator extends BaseComponent {
  private configManager: ConfigManager;

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager) {
    super(state, config);
    this.configManager = configManager;
  }

  public async render(): Promise<void> {
    // This component is used inline, no standalone render needed
  }

  public async configureProvider(providerName: string): Promise<boolean> {
    const provider = SUPPORTED_PROVIDERS[providerName];
    if (!provider) {
      await this.showError(`Unknown provider: ${providerName}`);
      return false;
    }

    console.log(this.utils.formatHeader(`🔧 Configuring ${provider.displayName}`));

    try {
      // Check if provider is already configured, if not, use undefined
      let existingConfig;
      try {
        existingConfig = this.configManager.getProviderConfig(providerName);
      } catch (error) {
        // Provider not configured yet, which is expected for new configurations
        existingConfig = undefined;
      }

      const configuration = await this.gatherProviderConfiguration(provider, existingConfig);

      if (!configuration) {
        return false; // User cancelled
      }

      await this.saveProviderConfiguration(providerName, configuration);
      await this.handleFirstProviderSetup(providerName);

      await this.showSuccess(`${provider.displayName} configured successfully!`);
      return true;
    } catch (error) {
      await this.showError(`Failed to configure provider: ${error}`);
      return false;
    }
  }

  private async gatherProviderConfiguration(provider: any, existingConfig?: any) {
    const questions: any[] = [
      {
        type: 'password',
        name: 'apiKey',
        message: `Enter your ${provider.displayName} API key:`,
        default: existingConfig?.apiKey,
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'API key is required';
          }
          return true;
        },
        mask: '*',
      },
      {
        type: 'list',
        name: 'defaultModel',
        message: 'Choose default model:',
        choices: provider.models.map((model: string) => ({
          name: this.formatModelChoice(model),
          value: model,
        })),
        default: existingConfig?.defaultModel || provider.models[0],
      },
    ];

    // Add base URL configuration for providers that support it
    if (provider.baseUrl) {
      questions.push({
        type: 'input',
        name: 'baseUrl',
        message: 'Base URL (optional):',
        default: existingConfig?.baseUrl || provider.baseUrl,
        validate: (input: string) => {
          if (input && !this.isValidUrl(input)) {
            return 'Please enter a valid URL';
          }
          return true;
        },
      });
    }

    // Add advanced configuration option
    questions.push({
      type: 'confirm',
      name: 'configureAdvanced',
      message: 'Configure advanced settings?',
      default: false,
    });

    const answers = await inquirer.prompt(questions);

    if (answers.configureAdvanced) {
      const advancedConfig = await this.gatherAdvancedConfiguration();
      Object.assign(answers, advancedConfig);
    }

    return answers;
  }

  private async gatherAdvancedConfiguration() {
    return await inquirer.prompt([
      {
        type: 'number',
        name: 'maxTokens',
        message: 'Maximum tokens (optional):',
        validate: (input: number) => {
          if (input && (input < 1 || input > 100000)) {
            return 'Max tokens must be between 1 and 100000';
          }
          return true;
        },
      },
      {
        type: 'number',
        name: 'temperature',
        message: 'Temperature (0.0 - 2.0, optional):',
        validate: (input: number) => {
          if (input && (input < 0 || input > 2)) {
            return 'Temperature must be between 0.0 and 2.0';
          }
          return true;
        },
      },
      {
        type: 'input',
        name: 'systemPrompt',
        message: 'Custom system prompt (optional):',
      },
    ]);
  }

  private async saveProviderConfiguration(providerName: string, configuration: any): Promise<void> {
    await this.configManager.setProvider(providerName, {
      apiKey: configuration.apiKey,
      defaultModel: configuration.defaultModel,
      baseUrl: configuration.baseUrl,
      maxTokens: configuration.maxTokens,
      temperature: configuration.temperature,
      enabled: true,
      advanced: configuration.advanced || {}
    });

    // Save advanced configuration if provided
    if (configuration.maxTokens || configuration.temperature || configuration.systemPrompt) {
      const config = this.configManager.getConfig();
      if (!config.providers[providerName].advanced) {
        config.providers[providerName].advanced = {};
      }
      
      if (configuration.maxTokens) {
        config.providers[providerName].advanced.maxTokens = configuration.maxTokens;
      }
      if (configuration.temperature) {
        config.providers[providerName].advanced.temperature = configuration.temperature;
      }
      if (configuration.systemPrompt) {
        config.providers[providerName].advanced.systemPrompt = configuration.systemPrompt;
      }
      
      await this.configManager.saveConfig();
    }
  }

  private async handleFirstProviderSetup(providerName: string): Promise<void> {
    const configuredProviders = this.configManager.getConfiguredProviders();
    
    // Set as default if it's the first provider
    if (configuredProviders.length === 1) {
      await this.configManager.setDefaultProvider(providerName);
      this.utils.showInfo(`Set ${SUPPORTED_PROVIDERS[providerName].displayName} as default provider`);
    }
  }

  public async testProviderConnection(providerName: string): Promise<boolean> {
    this.utils.startSpinner(`Testing connection to ${SUPPORTED_PROVIDERS[providerName].displayName}...`);
    
    try {
      // TODO: Implement actual connection test
      // This would involve creating a provider client and making a test request
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate test
      
      this.utils.stopSpinner(true, 'Connection test successful');
      return true;
    } catch (error) {
      this.utils.stopSpinner(false, `Connection test failed: ${error}`);
      return false;
    }
  }

  private formatModelChoice(model: string): string {
    // Add model descriptions or capabilities if available
    const modelDescriptions: Record<string, string> = {
      'gpt-4': 'Most capable, best for complex tasks',
      'gpt-4-turbo': 'Fast and capable, good balance',
      'gpt-3.5-turbo': 'Fast and efficient, good for simple tasks',
      'claude-3-opus-20240229': 'Most capable Claude model',
      'claude-3-sonnet-20240229': 'Balanced performance and speed',
      'claude-3-haiku-20240307': 'Fastest Claude model',
      'gemini-pro': 'Google\'s most capable model',
      'gemini-pro-vision': 'Supports images and text',
      'deepseek-chat': 'General conversation model',
      'deepseek-coder': 'Specialized for coding tasks',
    };

    const description = modelDescriptions[model];
    return description ? `${model} - ${description}` : model;
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}
