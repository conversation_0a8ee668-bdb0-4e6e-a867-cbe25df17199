import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';
export interface WebFetchResult {
    url: string;
    statusCode: number;
    headers: Record<string, string>;
    content: string;
    contentType: string;
    size: number;
    redirects: string[];
}
export declare class WebFetchTool extends BaseTool {
    name: string;
    description: string;
    requiresConfirmation: boolean;
    parameters: {
        type: "object";
        properties: {
            url: {
                type: string;
                description: string;
            };
            method: {
                type: string;
                enum: string[];
                description: string;
                default: string;
            };
            headers: {
                type: string;
                description: string;
                additionalProperties: {
                    type: string;
                };
            };
            body: {
                type: string;
                description: string;
            };
            timeout: {
                type: string;
                description: string;
                default: number;
            };
            follow_redirects: {
                type: string;
                description: string;
                default: boolean;
            };
            max_redirects: {
                type: string;
                description: string;
                default: number;
            };
            user_agent: {
                type: string;
                description: string;
                default: string;
            };
            extract_text: {
                type: string;
                description: string;
                default: boolean;
            };
            max_content_size: {
                type: string;
                description: string;
                default: number;
            };
        };
        required: string[];
    };
    constructor();
    validate(params: Record<string, any>): Promise<ToolValidationResult>;
    execute(params: Record<string, any>): Promise<ToolResult>;
    private fetchUrl;
    private extractTextFromHtml;
    private formatWebResponse;
}
//# sourceMappingURL=WebFetchTool.d.ts.map