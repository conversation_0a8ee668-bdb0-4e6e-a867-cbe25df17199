import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';
export interface SearchResult {
    title: string;
    url: string;
    snippet: string;
    displayUrl: string;
}
export interface WebSearchResult {
    query: string;
    results: SearchResult[];
    totalResults: number;
    searchTime: number;
    provider: string;
}
export declare class WebSearchTool extends BaseTool {
    name: string;
    description: string;
    requiresConfirmation: boolean;
    parameters: {
        type: "object";
        properties: {
            query: {
                type: string;
                description: string;
            };
            num_results: {
                type: string;
                description: string;
                default: number;
                minimum: number;
                maximum: number;
            };
            search_engine: {
                type: string;
                enum: string[];
                description: string;
                default: string;
            };
            safe_search: {
                type: string;
                description: string;
                default: boolean;
            };
            region: {
                type: string;
                description: string;
                default: string;
            };
            time_range: {
                type: string;
                enum: string[];
                description: string;
                default: string;
            };
        };
        required: string[];
    };
    constructor();
    validate(params: Record<string, any>): Promise<ToolValidationResult>;
    execute(params: Record<string, any>): Promise<ToolResult>;
    private searchDuckDuckGo;
    private searchBing;
    private searchGoogle;
    private formatSearchResults;
}
//# sourceMappingURL=WebSearchTool.d.ts.map