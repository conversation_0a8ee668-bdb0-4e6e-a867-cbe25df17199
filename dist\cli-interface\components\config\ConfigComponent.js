"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigComponent = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const BaseComponent_1 = require("../common/BaseComponent");
const types_1 = require("../../../core/types");
class ConfigComponent extends BaseComponent_1.BaseComponent {
    configManager;
    constructor(state, config, configManager) {
        super(state, config);
        this.configManager = configManager;
    }
    async render() {
        this.utils.clearScreen();
        this.utils.showBanner('⚙️ Configuration', 'Manage your AI CLI settings');
        await this.showMainMenu();
    }
    async showMainMenu() {
        const choices = [
            { name: '🤖 Manage Providers', value: 'providers' },
            { name: '🎨 Theme Settings', value: 'theme' },
            { name: '🔧 Terminal Settings', value: 'terminal' },
            { name: '💾 Export Configuration', value: 'export' },
            { name: '📥 Import Configuration', value: 'import' },
            { name: '🔄 Reset to Defaults', value: 'reset' },
            { name: '🏠 Back to Terminal', value: 'back' },
        ];
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'What would you like to configure?',
                choices,
            },
        ]);
        switch (action) {
            case 'providers':
                await this.manageProviders();
                break;
            case 'theme':
                await this.configureTheme();
                break;
            case 'terminal':
                await this.configureTerminal();
                break;
            case 'export':
                await this.exportConfiguration();
                break;
            case 'import':
                await this.importConfiguration();
                break;
            case 'reset':
                await this.resetConfiguration();
                break;
            case 'back':
                if (this.state.isAuthenticated) {
                    this.updateState({ currentView: 'terminal' });
                }
                else {
                    this.updateState({ currentView: 'auth' });
                }
                return;
        }
        // Show menu again unless we're navigating away
        await this.showMainMenu();
    }
    async manageProviders() {
        this.utils.clearScreen();
        this.utils.showBanner('🤖 Provider Management');
        const configuredProviders = this.configManager.getConfiguredProviders();
        const choices = [
            { name: '➕ Add New Provider', value: 'add' },
            ...configuredProviders.map(provider => ({
                name: `⚙️ Configure ${provider}`,
                value: `configure_${provider}`,
            })),
            ...configuredProviders.map(provider => ({
                name: `🗑️ Remove ${provider}`,
                value: `remove_${provider}`,
            })),
            { name: '🔙 Back', value: 'back' },
        ];
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'Provider Management:',
                choices,
            },
        ]);
        if (action === 'back') {
            return;
        }
        if (action === 'add') {
            await this.addProvider();
        }
        else if (action.startsWith('configure_')) {
            const provider = action.replace('configure_', '');
            await this.configureProvider(provider);
        }
        else if (action.startsWith('remove_')) {
            const provider = action.replace('remove_', '');
            await this.removeProvider(provider);
        }
    }
    async addProvider() {
        const configuredProviders = this.configManager.getConfiguredProviders();
        const availableProviders = Object.values(types_1.SUPPORTED_PROVIDERS)
            .filter(provider => !configuredProviders.includes(provider.name))
            .map(provider => ({
            name: `${provider.name} - ${provider.description}`,
            value: provider.name,
        }));
        if (availableProviders.length === 0) {
            this.utils.showWarning('All supported providers are already configured.');
            await this.utils.waitForKeyPress();
            return;
        }
        const { provider } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'provider',
                message: 'Select a provider to add:',
                choices: availableProviders,
            },
        ]);
        await this.configureProvider(provider);
    }
    async configureProvider(providerName) {
        const providerInfo = types_1.SUPPORTED_PROVIDERS[providerName];
        if (!providerInfo) {
            this.utils.showError(`Unknown provider: ${providerName}`);
            await this.utils.waitForKeyPress();
            return;
        }
        this.utils.clearScreen();
        this.utils.showBanner(`🔧 Configure ${providerName}`, providerInfo.description);
        const questions = [
            {
                type: 'input',
                name: 'apiKey',
                message: `Enter your ${providerName} API key:`,
                validate: (input) => input.length > 0 || 'API key is required',
            },
            {
                type: 'list',
                name: 'defaultModel',
                message: 'Select default model:',
                choices: providerInfo.models.map(model => ({
                    name: model,
                    value: model,
                })),
            },
        ];
        // Add provider-specific questions
        if (providerName === 'openai') {
            questions.push({
                type: 'input',
                name: 'baseURL',
                message: 'Base URL (optional, for custom endpoints):',
                default: 'https://api.openai.com/v1',
            });
        }
        if (providerName === 'google') {
            questions.push({
                type: 'input',
                name: 'projectId',
                message: 'Google Cloud Project ID (optional):',
            });
        }
        const answers = await inquirer_1.default.prompt(questions);
        try {
            await this.configManager.setProviderConfig(providerName, {
                apiKey: answers.apiKey,
                defaultModel: answers.defaultModel,
                ...answers,
            });
            this.utils.showSuccess(`${providerName} configured successfully!`);
            // Ask if this should be the default provider
            if (this.configManager.getConfiguredProviders().length === 1 ||
                await this.utils.confirmAction(`Set ${providerName} as default provider?`)) {
                await this.configManager.setDefaultProvider(providerName);
                this.updateState({
                    currentProvider: providerName,
                    currentModel: answers.defaultModel,
                });
            }
        }
        catch (error) {
            this.utils.showError(`Failed to configure ${providerName}: ${error instanceof Error ? error.message : String(error)}`);
        }
        await this.utils.waitForKeyPress();
    }
    async removeProvider(providerName) {
        const confirmed = await this.utils.confirmAction(`Are you sure you want to remove ${providerName}? This will delete all configuration data.`);
        if (confirmed) {
            try {
                await this.configManager.removeProviderConfig(providerName);
                this.utils.showSuccess(`${providerName} removed successfully!`);
                // If this was the current provider, switch to another or go to auth
                if (this.state.currentProvider === providerName) {
                    const remainingProviders = this.configManager.getConfiguredProviders();
                    if (remainingProviders.length > 0) {
                        const newProvider = remainingProviders[0];
                        const config = this.configManager.getProviderConfig(newProvider);
                        this.updateState({
                            currentProvider: newProvider,
                            currentModel: config.defaultModel,
                        });
                        await this.configManager.setDefaultProvider(newProvider);
                    }
                    else {
                        this.updateState({
                            isAuthenticated: false,
                            currentProvider: '',
                            currentModel: '',
                            currentView: 'auth',
                        });
                    }
                }
            }
            catch (error) {
                this.utils.showError(`Failed to remove ${providerName}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        await this.utils.waitForKeyPress();
    }
    async configureTheme() {
        this.utils.clearScreen();
        this.utils.showBanner('🎨 Theme Configuration');
        const currentTheme = this.config.theme;
        const colors = ['black', 'red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white', 'gray'];
        const themeQuestions = [
            {
                type: 'list',
                name: 'primary',
                message: 'Primary color:',
                choices: colors,
                default: currentTheme.primary,
            },
            {
                type: 'list',
                name: 'secondary',
                message: 'Secondary color:',
                choices: colors,
                default: currentTheme.secondary,
            },
            {
                type: 'list',
                name: 'success',
                message: 'Success color:',
                choices: colors,
                default: currentTheme.success,
            },
            {
                type: 'list',
                name: 'error',
                message: 'Error color:',
                choices: colors,
                default: currentTheme.error,
            },
            {
                type: 'list',
                name: 'warning',
                message: 'Warning color:',
                choices: colors,
                default: currentTheme.warning,
            },
        ];
        const newTheme = await inquirer_1.default.prompt(themeQuestions);
        // Update theme
        Object.assign(this.config.theme, newTheme);
        this.utils.showSuccess('Theme updated successfully!');
        await this.utils.waitForKeyPress();
    }
    async configureTerminal() {
        this.utils.clearScreen();
        this.utils.showBanner('🔧 Terminal Configuration');
        const currentTerminal = this.config.terminal;
        const terminalQuestions = [
            {
                type: 'input',
                name: 'prompt',
                message: 'Terminal prompt:',
                default: currentTerminal.prompt,
            },
            {
                type: 'number',
                name: 'maxHistorySize',
                message: 'Maximum history size:',
                default: currentTerminal.maxHistorySize,
                validate: (input) => input > 0 || 'Must be greater than 0',
            },
            {
                type: 'confirm',
                name: 'autoSave',
                message: 'Auto-save conversations:',
                default: currentTerminal.autoSave,
            },
            {
                type: 'confirm',
                name: 'showTimestamps',
                message: 'Show timestamps:',
                default: currentTerminal.showTimestamps,
            },
            {
                type: 'confirm',
                name: 'showTokenCounts',
                message: 'Show token counts:',
                default: currentTerminal.showTokenCounts,
            },
            {
                type: 'confirm',
                name: 'streamResponses',
                message: 'Stream AI responses:',
                default: currentTerminal.streamResponses,
            },
        ];
        const newTerminal = await inquirer_1.default.prompt(terminalQuestions);
        // Update terminal config
        Object.assign(this.config.terminal, newTerminal);
        this.utils.showSuccess('Terminal configuration updated successfully!');
        await this.utils.waitForKeyPress();
    }
    async exportConfiguration() {
        try {
            const config = await this.configManager.exportConfig();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `ai-cli-config-${timestamp}.json`;
            // In a real implementation, you'd save to file
            console.log('\nConfiguration export:');
            console.log(JSON.stringify(config, null, 2));
            console.log(`\nSave this to: ${filename}`);
            this.utils.showSuccess('Configuration exported successfully!');
        }
        catch (error) {
            this.utils.showError(`Export failed: ${error instanceof Error ? error.message : String(error)}`);
        }
        await this.utils.waitForKeyPress();
    }
    async importConfiguration() {
        this.utils.showWarning('Import functionality would require file selection dialog.');
        this.utils.showInfo('For now, you can manually edit the configuration files.');
        await this.utils.waitForKeyPress();
    }
    async resetConfiguration() {
        const confirmed = await this.utils.confirmAction('Are you sure you want to reset all configuration to defaults? This cannot be undone.');
        if (confirmed) {
            try {
                await this.configManager.resetToDefaults();
                this.utils.showSuccess('Configuration reset to defaults!');
                // Update state to reflect reset
                this.updateState({
                    isAuthenticated: false,
                    currentProvider: '',
                    currentModel: '',
                    currentView: 'auth',
                });
            }
            catch (error) {
                this.utils.showError(`Reset failed: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        await this.utils.waitForKeyPress();
    }
}
exports.ConfigComponent = ConfigComponent;
//# sourceMappingURL=ConfigComponent.js.map