import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';
export interface ShellExecutionResult {
    stdout: string;
    stderr: string;
    exitCode: number;
    duration: number;
    command: string;
    workingDirectory: string;
    processId?: number;
}
export declare class ShellTool extends BaseTool {
    name: string;
    description: string;
    requiresConfirmation: boolean;
    parameters: {
        type: "object";
        properties: {
            command: {
                type: string;
                description: string;
            };
            workingDirectory: {
                type: string;
                description: string;
            };
            timeout: {
                type: string;
                description: string;
                default: number;
            };
            environment: {
                type: string;
                description: string;
                additionalProperties: {
                    type: string;
                };
            };
            description: {
                type: string;
                description: string;
            };
        };
        required: string[];
    };
    private blockedCommands;
    private allowedCommands;
    constructor();
    validate(params: Record<string, any>): Promise<ToolValidationResult>;
    execute(params: Record<string, any>): Promise<ToolResult>;
    private executeCommand;
    private formatExecutionResult;
    shouldConfirmExecute(params: Record<string, any>): Promise<boolean>;
}
//# sourceMappingURL=ShellTool.d.ts.map