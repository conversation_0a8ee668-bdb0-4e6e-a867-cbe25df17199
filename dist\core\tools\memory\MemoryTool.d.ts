import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';
export interface MemoryEntry {
    id: string;
    content: string;
    tags: string[];
    timestamp: Date;
    importance: number;
    context?: string;
}
export interface MemorySearchResult {
    entries: MemoryEntry[];
    totalMatches: number;
    searchTime: number;
}
export declare class MemoryTool extends BaseTool {
    name: string;
    description: string;
    requiresConfirmation: boolean;
    private memoryFile;
    parameters: {
        type: "object";
        properties: {
            action: {
                type: string;
                enum: string[];
                description: string;
            };
            content: {
                type: string;
                description: string;
            };
            id: {
                type: string;
                description: string;
            };
            query: {
                type: string;
                description: string;
            };
            tags: {
                type: string;
                items: {
                    type: string;
                };
                description: string;
            };
            importance: {
                type: string;
                description: string;
                minimum: number;
                maximum: number;
                default: number;
            };
            context: {
                type: string;
                description: string;
            };
            limit: {
                type: string;
                description: string;
                default: number;
                minimum: number;
                maximum: number;
            };
        };
        required: string[];
    };
    constructor(memoryDir?: string);
    validate(params: Record<string, any>): Promise<ToolValidationResult>;
    execute(params: Record<string, any>): Promise<ToolResult>;
    private storeMemory;
    private retrieveMemory;
    private searchMemory;
    private listMemories;
    private deleteMemory;
    private updateMemory;
    private loadMemories;
    private saveMemories;
    private formatMemoryEntry;
    private formatSearchResults;
    private formatMemoryList;
    private generateId;
}
//# sourceMappingURL=MemoryTool.d.ts.map