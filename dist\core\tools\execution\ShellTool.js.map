{"version": 3, "file": "ShellTool.js", "sourceRoot": "", "sources": ["../../../../src/core/tools/execution/ShellTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAoD;AACpD,2CAA6B;AAC7B,uCAAyB;AACzB,+CAAkE;AAalE,MAAa,SAAU,SAAQ,mBAAQ;IAC9B,IAAI,GAAG,mBAAmB,CAAC;IAC3B,WAAW,GAAG,+DAA+D,CAAC;IAC9E,oBAAoB,GAAG,IAAI,CAAC;IAE5B,UAAU,GAAG;QAClB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,8BAA8B;aAC5C;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oDAAoD;aAClE;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,0CAA0C;gBACvD,OAAO,EAAE,KAAK;aACf;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,kCAAkC;gBAC/C,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aACzC;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,qDAAqD;aACnE;SACF;QACD,QAAQ,EAAE,CAAC,SAAS,CAAC;KACtB,CAAC;IAEM,eAAe,GAAG;QACxB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ;QAC9B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;QAC9B,IAAI,EAAE,MAAM,EAAE,OAAO;QACrB,UAAU,EAAE,QAAQ,EAAE,MAAM;QAC5B,MAAM,EAAE,SAAS,EAAE,OAAO;QAC1B,QAAQ,EAAE,UAAU;QACpB,KAAK,EAAE,SAAS;QAChB,WAAW,EAAE,SAAS;QACtB,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;QACzC,OAAO,EAAE,QAAQ;QACjB,UAAU,EAAE,KAAK,EAAE,cAAc;KAClC,CAAC;IAEM,eAAe,GAAG;QACxB,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;QAC1C,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;QAC1C,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ;QAC7B,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;QACzC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK;QACzC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI;QAC1C,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO;QAC5C,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;QAC7B,QAAQ,EAAE,SAAS;QACnB,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;QACvC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI;QACvC,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK;QAClC,SAAS,EAAE,OAAO,EAAE,SAAS;KAC9B,CAAC;IAEF;QACE,KAAK,CAAC;YACJ,QAAQ,EAAE,WAAW;YACrB,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC;YACjD,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI;YACf,oBAAoB,EAAE,IAAI;SAC3B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,MAA2B;QAC/C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC1D,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAEtC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACzC,CAAC;YAED,6BAA6B;YAC7B,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAElD,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;gBACxE,MAAM,CAAC,IAAI,CAAC,YAAY,WAAW,mCAAmC,CAAC,CAAC;YAC1E,CAAC;YAED,+BAA+B;YAC/B,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9E,QAAQ,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YAC1E,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,QAAQ,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACtE,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpD,QAAQ,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACpD,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACzD,CAAC;YAED,0BAA0B;YAC1B,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,IAAI,OAAO,MAAM,CAAC,gBAAgB,KAAK,QAAQ,EAAE,CAAC;oBAChD,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACN,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;oBACtD,MAAM,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;oBAC7B,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;oBAEjC,+EAA+E;oBAC/E,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;wBACpE,MAAM,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;oBAC9F,CAAC;gBACH,CAAC;YACH,CAAC;YAED,gBAAgB;YAChB,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;gBACjC,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC;oBACzF,MAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;gBACtF,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,MAA2B;QAC9C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACtC,MAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QACzG,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC;QACxC,MAAM,WAAW,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC,EAAE,CAAC;QAEtE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YAE1F,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAC1D,MAAM,QAAQ,GAAG;gBACf,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;gBACzC,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC;YAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC,iBAAiB,CAC3B,iCAAiC,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,MAAM,EAAE,EACpE,QAAQ,CACT,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,OAAe,EACf,gBAAwB,EACxB,OAAe,EACf,WAAmC;QAEnC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,uDAAuD;YACvD,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC;YAC5C,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;YAClD,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAEhE,MAAM,YAAY,GAAiB,IAAA,qBAAK,EAAC,KAAK,EAAE,SAAS,EAAE;gBACzD,GAAG,EAAE,gBAAgB;gBACrB,GAAG,EAAE,WAAW;gBAChB,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;gBAC/B,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;YAEH,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,aAA6B,CAAC;YAElC,iBAAiB;YACjB,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7B,UAAU,CAAC,GAAG,EAAE;oBACd,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;wBACzB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC/B,CAAC;gBACH,CAAC,EAAE,IAAI,CAAC,CAAC;gBACT,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,OAAO,IAAI,CAAC,CAAC,CAAC;YAC5D,CAAC,EAAE,OAAO,CAAC,CAAC;YAEZ,iBAAiB;YACjB,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;gBACxB,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACtC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;gBACxB,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACtC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC;YAED,4BAA4B;YAC5B,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACpC,YAAY,CAAC,aAAa,CAAC,CAAC;gBAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAExC,OAAO,CAAC;oBACN,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;oBACrB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;oBACrB,QAAQ,EAAE,QAAQ,IAAI,CAAC;oBACvB,QAAQ;oBACR,OAAO;oBACP,gBAAgB;oBAChB,SAAS,EAAE,YAAY,CAAC,GAAG;iBAC5B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACjC,YAAY,CAAC,aAAa,CAAC,CAAC;gBAC5B,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,MAA4B;QACxD,MAAM,KAAK,GAAG;YACZ,YAAY,MAAM,CAAC,OAAO,EAAE;YAC5B,sBAAsB,MAAM,CAAC,gBAAgB,EAAE;YAC/C,cAAc,MAAM,CAAC,QAAQ,EAAE;YAC/B,aAAa,MAAM,CAAC,QAAQ,IAAI;YAChC,EAAE;SACH,CAAC;QAEF,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,MAA2B;QAC3D,4EAA4E;QAC5E,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAE1D,iDAAiD;QACjD,MAAM,YAAY,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACpE,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3F,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA9RD,8BA8RC"}