import { ChatMessage, BaseTool } from '../types';
import { ToolRegistry, ToolExecutionResult } from '../tools/base/ToolRegistry';
import { ConfigManager } from '../config/ConfigManager';
export interface AIEngineOptions {
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
    tools?: BaseTool[];
    streamResponse?: boolean;
}
export interface AIResponse {
    message: ChatMessage;
    toolCalls?: ToolExecutionResult[];
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    finishReason?: string;
}
export interface ToolConfirmationHandler {
    confirmExecution(toolName: string, parameters: Record<string, any>): Promise<boolean>;
    confirmToolExecution(toolName: string, parameters: Record<string, any>): Promise<boolean>;
}
export declare class AIEngine {
    private configManager;
    private toolRegistry;
    private toolExecutionEngine;
    private providerFactory;
    private toolConfirmationHandler?;
    constructor(configManager: ConfigManager, toolRegistry: ToolRegistry, toolConfirmationHandler?: ToolConfirmationHandler);
    setToolConfirmationHandler(handler: ToolConfirmationHandler): void;
    processMessage(messages: ChatMessage[], options?: AIEngineOptions): Promise<AIResponse>;
    streamMessage(messages: ChatMessage[], options?: AIEngineOptions): AsyncGenerator<string, AIResponse>;
    private getProvider;
    private getAvailableTools;
    private executeToolCall;
    private createToolMessages;
    private generateId;
    testProvider(providerName?: string): Promise<boolean>;
    getAvailableModels(providerName?: string): Promise<string[]>;
    getRegisteredTools(): any[];
    getToolsByCategory(category: string): any[];
    isToolEnabled(toolName: string): boolean;
    enableTool(toolName: string): void;
    disableTool(toolName: string): void;
}
//# sourceMappingURL=AIEngine.d.ts.map