{"version": 3, "file": "WebFetchTool.js", "sourceRoot": "", "sources": ["../../../../src/core/tools/web/WebFetchTool.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAC7C,6BAA0B;AAC1B,+CAAkE;AAalE,MAAa,YAAa,SAAQ,mBAAQ;IACjC,IAAI,GAAG,WAAW,CAAC;IACnB,WAAW,GAAG,gFAAgF,CAAC;IAC/F,oBAAoB,GAAG,KAAK,CAAC;IAE7B,UAAU,GAAG;QAClB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,GAAG,EAAE;gBACH,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,2BAA2B;aACzC;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;gBAC9C,WAAW,EAAE,oBAAoB;gBACjC,OAAO,EAAE,KAAK;aACf;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,iCAAiC;gBAC9C,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aACzC;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oCAAoC;aAClD;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,iCAAiC;gBAC9C,OAAO,EAAE,KAAK;aACf;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,kCAAkC;gBAC/C,OAAO,EAAE,IAAI;aACd;YACD,aAAa,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,uCAAuC;gBACpD,OAAO,EAAE,CAAC;aACX;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,0BAA0B;gBACvC,OAAO,EAAE,qBAAqB;aAC/B;YACD,YAAY,EAAE;gBACZ,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,2CAA2C;gBACxD,OAAO,EAAE,IAAI;aACd;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,2CAA2C;gBACxD,OAAO,EAAE,QAAQ,EAAE,OAAO;aAC3B;SACF;QACD,QAAQ,EAAE,CAAC,KAAK,CAAC;KAClB,CAAC;IAEF;QACE,KAAK,CAAC;YACJ,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC;YAC1C,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,KAAK;YAChB,oBAAoB,EAAE,KAAK;SAC5B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,MAA2B;QAC/C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,+BAA+B;QAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,eAAe;QACf,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEhC,iBAAiB;YACjB,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACrD,CAAC;YAED,2DAA2D;YAC3D,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW;gBAC5B,GAAG,CAAC,QAAQ,KAAK,WAAW;gBAC5B,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC;gBACnC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;gBAC9B,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpC,QAAQ,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,uBAAuB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/F,CAAC;QAED,+BAA+B;QAC/B,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAChC,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC9D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,0BAA0B,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE;gBACjE,GAAG,EAAE,IAAI;gBACT,GAAG,EAAE,MAAM,EAAE,gBAAgB;aAC9B,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,aAAa,EAAE,eAAe,EAAE;gBAC7E,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,EAAE;gBACP,OAAO,EAAE,IAAI;aACd,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,MAAM,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,gBAAgB,EAAE,kBAAkB,EAAE;gBACnF,GAAG,EAAE,IAAI,EAAE,cAAc;gBACzB,GAAG,EAAE,SAAS,EAAE,gBAAgB;aACjC,CAAC,CAAC,CAAC;QACN,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,MAA2B;QAC9C,IAAI,CAAC;YACH,MAAM,EACJ,GAAG,EACH,MAAM,GAAG,KAAK,EACd,OAAO,GAAG,EAAE,EACZ,IAAI,EACJ,OAAO,GAAG,KAAK,EACf,gBAAgB,GAAG,IAAI,EACvB,aAAa,GAAG,CAAC,EACjB,UAAU,GAAG,qBAAqB,EAClC,YAAY,GAAG,IAAI,EACnB,gBAAgB,GAAG,QAAQ,GAC5B,GAAG,MAAM,CAAC;YAEX,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC;gBACjC,GAAG;gBACH,MAAM;gBACN,OAAO,EAAE;oBACP,YAAY,EAAE,UAAU;oBACxB,GAAG,OAAO;iBACX;gBACD,IAAI;gBACJ,OAAO;gBACP,gBAAgB;gBAChB,aAAa;gBACb,gBAAgB;aACjB,CAAC,CAAC;YAEH,IAAI,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC;YAEtC,sCAAsC;YACtC,IAAI,YAAY,IAAI,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC7D,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC9D,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAExE,OAAO,IAAI,CAAC,mBAAmB,CAC7B,gBAAgB,EAChB,cAAc,EACd;gBACE,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACjF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,OAStB;QACC,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,IAAI,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC;QAE7B,MAAM,WAAW,GAAG;YAClB,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,WAAW,EAAS;YAC3C,GAAG,EAAE,UAAU;YACf,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,YAAY,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAClE,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,iCAAiC;SAC9D,CAAC;QAEF,MAAM,QAAQ,GAAkB,MAAM,IAAA,eAAK,EAAC,WAAW,CAAC,CAAC;QAEzD,OAAO;YACL,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,UAAU;YACtC,UAAU,EAAE,QAAQ,CAAC,MAAM;YAC3B,OAAO,EAAE,QAAQ,CAAC,OAAiC;YACnD,OAAO,EAAE,QAAQ,CAAC,IAAI;YACtB,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,SAAS;YAC1D,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;YAC9C,SAAS;SACV,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACtC,4CAA4C;QAC5C,OAAO,IAAI;aACR,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC;aAChD,OAAO,CAAC,iCAAiC,EAAE,EAAE,CAAC;aAC9C,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;aACvB,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;aACvB,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;aACtB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;aACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;aACrB,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;aACvB,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;aACtB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,IAAI,EAAE,CAAC;IACZ,CAAC;IAEO,iBAAiB,CAAC,MAAsB,EAAE,OAAe;QAC/D,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC/B,KAAK,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;QACjC,KAAK,CAAC,IAAI,CAAC,WAAW,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;QAC3C,KAAK,CAAC,IAAI,CAAC,iBAAiB,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;QAClD,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAExD,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,KAAK,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAEhD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;CACF;AA5QD,oCA4QC"}