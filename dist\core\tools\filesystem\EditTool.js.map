{"version": 3, "file": "EditTool.js", "sourceRoot": "", "sources": ["../../../../src/core/tools/filesystem/EditTool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA+B;AAC/B,2CAA6B;AAC7B,+CAAkE;AAGlE,MAAa,QAAS,SAAQ,mBAAQ;IAC7B,IAAI,GAAG,SAAS,CAAC;IACjB,WAAW,GAAG,yEAAyE,CAAC;IACxF,oBAAoB,GAAG,IAAI,CAAC;IAE5B,UAAU,GAAG;QAClB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,SAAS,EAAE;gBACT,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,iDAAiD;aAC/D;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,4EAA4E;aAC1F;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,qCAAqC;aACnD;YACD,qBAAqB,EAAE;gBACrB,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,kDAAkD;gBAC/D,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,CAAC;aACX;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,6CAA6C;gBAC1D,OAAO,EAAE,IAAI;aACd;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,mCAAmC;gBAChD,OAAO,EAAE,KAAK;aACf;YACD,eAAe,EAAE;gBACf,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,iDAAiD;gBAC9D,OAAO,EAAE,IAAI;aACd;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,qDAAqD;gBAClE,OAAO,EAAE,KAAK;aACf;SACF;QACD,QAAQ,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC;KACpD,CAAC;IAEF;QACE,KAAK,CAAC;YACJ,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;YAC3C,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI;YACf,oBAAoB,EAAE,IAAI;SAC3B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,MAA2B;QAC/C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,+BAA+B;QAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC;QAC1G,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,qBAAqB;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAC3E,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;QAE3B,mBAAmB;QACnB,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAClF,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;QAEhC,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,EAAE;YAChF,UAAU,EAAE,IAAI,EAAE,qCAAqC;SACxD,CAAC,CAAC;QACH,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;QAEhC,+BAA+B;QAC/B,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,qBAAqB,EAAE,uBAAuB,EAAE;gBAC7F,GAAG,EAAE,CAAC;gBACN,OAAO,EAAE,IAAI;aACd,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,MAAM,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,CAAC;QACzF,CAAC;QAED,IAAI,MAAM,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACpD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1C,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBACpB,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,SAAS,iBAAiB,CAAC,CAAC;YAC1D,CAAC;iBAAM,CAAC;gBACN,4BAA4B;gBAC5B,IAAI,CAAC;oBACH,MAAM,EAAE,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACnD,CAAC;gBAAC,MAAM,CAAC;oBACP,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,SAAS,mBAAmB,CAAC,CAAC;gBAC5D,CAAC;gBAED,6CAA6C;gBAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpB,IAAI,CAAC;wBACH,MAAM,EAAE,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBACnD,CAAC;oBAAC,MAAM,CAAC;wBACP,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,SAAS,mBAAmB,CAAC,CAAC;oBAC5D,CAAC;gBACH,CAAC;gBAED,yBAAyB;gBACzB,IAAI,KAAK,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,OAAO;oBAC1C,QAAQ,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;gBACrG,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAK,KAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,SAAS,kBAAkB,CAAC,CAAC;YAC3D,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,uBAAuB,MAAM,CAAC,SAAS,MAAM,KAAK,EAAE,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,MAA2B;QAC9C,IAAI,CAAC;YACH,MAAM,EACJ,SAAS,EACT,UAAU,EACV,UAAU,EACV,qBAAqB,GAAG,CAAC,EACzB,cAAc,GAAG,IAAI,EACrB,gBAAgB,GAAG,KAAK,EACxB,eAAe,GAAG,IAAI,EACtB,OAAO,GAAG,KAAK,GAChB,GAAG,MAAM,CAAC;YAEX,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE7C,4BAA4B;YAC5B,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAEhE,0BAA0B;YAC1B,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,kBAAkB,CACvE,eAAe,EACf,UAAU,EACV,UAAU,EACV,cAAc,EACd,gBAAgB,CACjB,CAAC;YAEF,6BAA6B;YAC7B,IAAI,gBAAgB,KAAK,qBAAqB,EAAE,CAAC;gBAC/C,OAAO,IAAI,CAAC,iBAAiB,CAC3B,YAAY,qBAAqB,2BAA2B,gBAAgB,UAAU,CACvF,CAAC;YACJ,CAAC;YAED,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC,iBAAiB,CAC3B,6CAA6C,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,CACtF,CAAC;YACJ,CAAC;YAED,6CAA6C;YAC7C,IAAI,UAA8B,CAAC;YACnC,IAAI,eAAe,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChC,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YACrD,CAAC;YAED,yCAAyC;YACzC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YACvD,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1C,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,YAAY;gBAClB,YAAY,EAAE,eAAe,CAAC,MAAM;gBACpC,OAAO,EAAE,UAAU,CAAC,MAAM;gBAC1B,gBAAgB;gBAChB,UAAU;gBACV,MAAM,EAAE,OAAO;gBACf,OAAO;aACR,CAAC;YAEF,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAC9C,SAAS,EACT,OAAO,EACP,gBAAgB,EAChB,OAAO,EACP,UAAU,CACX,CAAC;YAEF,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,0BAA0B,CAAC;YAE3E,OAAO,IAAI,CAAC,mBAAmB,CAC7B,GAAG,MAAM,KAAK,gBAAgB,2BAA2B,SAAS,EAAE,EACpE,cAAc,EACd,QAAQ,CACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACjF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,kBAAkB,CACxB,OAAe,EACf,SAAiB,EACjB,SAAiB,EACjB,aAAsB,EACtB,cAAuB;QAWvB,MAAM,OAAO,GAKR,EAAE,CAAC;QAER,IAAI,YAAY,GAAG,SAAS,CAAC;QAC7B,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC,qBAAqB;QAEtC,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,IAAI,cAAc,EAAE,CAAC;YACnB,0DAA0D;YAC1D,YAAY,GAAG,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC;QAClF,CAAC;aAAM,CAAC;YACN,kCAAkC;YAClC,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,gBAAgB,GAAG,CAAC,CAAC;QAEzB,kDAAkD;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAElC,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBAC/C,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;oBACrB,OAAO,CAAC,IAAI,CAAC;wBACX,UAAU,EAAE,CAAC,GAAG,CAAC;wBACjB,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,OAAO;wBAChB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;qBAC7B,CAAC,CAAC;oBACH,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;oBACnB,gBAAgB,IAAI,OAAO,CAAC,MAAM,CAAC;gBACrC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5B,gBAAgB;YAChB,OAAO;SACR,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,GAAG,QAAQ,WAAW,SAAS,EAAE,CAAC;QAErD,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpC,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,oBAAoB,CAC1B,QAAgB,EAChB,OAKE,EACF,gBAAwB,EACxB,MAAe,EACf,UAAmB;QAEnB,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,cAAc,QAAQ,QAAQ,EAAE,CAAC,CAAC;QAChF,KAAK,CAAC,IAAI,CAAC,iBAAiB,gBAAgB,EAAE,CAAC,CAAC;QAEhD,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,CAAC,IAAI,CAAC,mBAAmB,UAAU,EAAE,CAAC,CAAC;QAC9C,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,wBAAwB;YACnE,KAAK,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;YACzC,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAClC,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YAClC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,MAAM,GAAG,EAAE,eAAe,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;CACF;AA9VD,4BA8VC"}