"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleProgressIndicator = exports.CLIProgressIndicator = void 0;
const ora_1 = __importDefault(require("ora"));
const chalk_1 = __importDefault(require("chalk"));
class CLIProgressIndicator {
    spinner = null;
    theme;
    constructor(theme) {
        this.theme = theme;
    }
    start(message) {
        if (this.spinner) {
            this.spinner.stop();
        }
        this.spinner = (0, ora_1.default)({
            text: message,
            color: this.theme.primary,
        }).start();
    }
    update(message) {
        if (this.spinner) {
            this.spinner.text = message;
        }
    }
    succeed(message) {
        if (this.spinner) {
            this.spinner.succeed(message);
            this.spinner = null;
        }
    }
    fail(message) {
        if (this.spinner) {
            this.spinner.fail(message);
            this.spinner = null;
        }
    }
    stop() {
        if (this.spinner) {
            this.spinner.stop();
            this.spinner = null;
        }
    }
}
exports.CLIProgressIndicator = CLIProgressIndicator;
class SimpleProgressIndicator {
    theme;
    constructor(theme) {
        this.theme = theme;
    }
    start(message) {
        console.log(chalk_1.default.blue(`⏳ ${message}`));
    }
    update(message) {
        console.log(chalk_1.default.blue(`⏳ ${message}`));
    }
    succeed(message) {
        if (message) {
            console.log(chalk_1.default.green(`✅ ${message}`));
        }
    }
    fail(message) {
        if (message) {
            console.log(chalk_1.default.red(`❌ ${message}`));
        }
    }
    stop() {
        // No-op for simple indicator
    }
}
exports.SimpleProgressIndicator = SimpleProgressIndicator;
//# sourceMappingURL=ProgressIndicator.js.map