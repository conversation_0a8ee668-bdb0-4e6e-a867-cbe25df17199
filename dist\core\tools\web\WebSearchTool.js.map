{"version": 3, "file": "WebSearchTool.js", "sourceRoot": "", "sources": ["../../../../src/core/tools/web/WebSearchTool.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,+CAAkE;AAkBlE,MAAa,aAAc,SAAQ,mBAAQ;IAClC,IAAI,GAAG,YAAY,CAAC;IACpB,WAAW,GAAG,6EAA6E,CAAC;IAC5F,oBAAoB,GAAG,KAAK,CAAC;IAE7B,UAAU,GAAG;QAClB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,yBAAyB;aACvC;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oCAAoC;gBACjD,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,EAAE;aACZ;YACD,aAAa,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,CAAC;gBACtC,WAAW,EAAE,sBAAsB;gBACnC,OAAO,EAAE,YAAY;aACtB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,8BAA8B;gBAC3C,OAAO,EAAE,IAAI;aACd;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,qDAAqD;gBAClE,OAAO,EAAE,IAAI;aACd;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;gBAC7C,WAAW,EAAE,+BAA+B;gBAC5C,OAAO,EAAE,KAAK;aACf;SACF;QACD,QAAQ,EAAE,CAAC,OAAO,CAAC;KACpB,CAAC;IAEF;QACE,KAAK,CAAC;YACJ,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC;YACtC,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,KAAK;YAChB,oBAAoB,EAAE,KAAK;SAC5B,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,MAA2B;QAC/C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,+BAA+B;QAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,iBAAiB;QACjB,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACpE,MAAM,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAE5B,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QACjE,CAAC;QAED,+BAA+B;QAC/B,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,WAAW,EAAE,aAAa,EAAE;gBACzE,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,EAAE;gBACP,OAAO,EAAE,IAAI;aACd,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,YAAY,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YACtD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;gBACjD,MAAM,CAAC,IAAI,CAAC,iCAAiC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAC5D,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7C,MAAM,CAAC,IAAI,CAAC,8BAA8B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,MAA2B;QAC9C,IAAI,CAAC;YACH,MAAM,EACJ,KAAK,EACL,WAAW,GAAG,EAAE,EAChB,aAAa,GAAG,YAAY,EAC5B,WAAW,GAAG,IAAI,EAClB,MAAM,GAAG,IAAI,EACb,UAAU,GAAG,KAAK,GACnB,GAAG,MAAM,CAAC;YAEX,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,IAAI,YAA6B,CAAC;YAElC,QAAQ,aAAa,EAAE,CAAC;gBACtB,KAAK,YAAY;oBACf,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;oBACpF,MAAM;gBACR,KAAK,MAAM;oBACT,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;oBAC1F,MAAM;gBACR,KAAK,QAAQ;oBACX,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;oBAC5F,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;YACnE,CAAC;YAED,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEjD,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YAEhE,OAAO,IAAI,CAAC,mBAAmB,CAC7B,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,EACrC,gBAAgB,EAChB;gBACE,KAAK,EAAE,YAAY,CAAC,KAAK;gBACzB,YAAY,EAAE,YAAY,CAAC,YAAY;gBACvC,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,UAAU,EAAE,YAAY,CAAC,UAAU;aACpC,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,sBAAsB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/E,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,KAAa,EACb,UAAkB,EAClB,UAAmB,EACnB,MAAc;QAEd,IAAI,CAAC;YACH,mDAAmD;YACnD,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,6BAA6B,EAAE;gBAC9D,MAAM,EAAE;oBACN,CAAC,EAAE,KAAK;oBACR,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE,GAAG;oBACZ,aAAa,EAAE,GAAG;oBAClB,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;iBAC3C;gBACD,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,MAAM,OAAO,GAAmB,EAAE,CAAC;YAEnC,yBAAyB;YACzB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,IAAI,CAAC,OAAO,IAAI,2BAA2B;oBAClD,GAAG,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;oBAC3B,OAAO,EAAE,IAAI,CAAC,YAAY;oBAC1B,UAAU,EAAE,IAAI,CAAC,cAAc,IAAI,EAAE;iBACtC,CAAC,CAAC;YACL,CAAC;YAED,yBAAyB;YACzB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7E,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACjC,OAAO,CAAC,IAAI,CAAC;4BACX,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe;4BACpD,GAAG,EAAE,KAAK,CAAC,QAAQ;4BACnB,OAAO,EAAE,KAAK,CAAC,IAAI;4BACnB,UAAU,EAAE,KAAK,CAAC,QAAQ;yBAC3B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO;gBACL,KAAK;gBACL,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC;gBACrC,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,UAAU,EAAE,CAAC;gBACb,QAAQ,EAAE,YAAY;aACvB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACzG,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CACtB,KAAa,EACb,UAAkB,EAClB,UAAmB,EACnB,MAAc,EACd,SAAiB;QAEjB,+CAA+C;QAC/C,+CAA+C;QAC/C,OAAO;YACL,KAAK;YACL,OAAO,EAAE,CAAC;oBACR,KAAK,EAAE,4BAA4B;oBACnC,GAAG,EAAE,sBAAsB;oBAC3B,OAAO,EAAE,4CAA4C;oBACrD,UAAU,EAAE,UAAU;iBACvB,CAAC;YACF,YAAY,EAAE,CAAC;YACf,UAAU,EAAE,CAAC;YACb,QAAQ,EAAE,uBAAuB;SAClC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,KAAa,EACb,UAAkB,EAClB,UAAmB,EACnB,MAAc,EACd,SAAiB;QAEjB,wDAAwD;QACxD,+CAA+C;QAC/C,OAAO;YACL,KAAK;YACL,OAAO,EAAE,CAAC;oBACR,KAAK,EAAE,8BAA8B;oBACrC,GAAG,EAAE,wBAAwB;oBAC7B,OAAO,EAAE,4DAA4D;oBACrE,UAAU,EAAE,YAAY;iBACzB,CAAC;YACF,YAAY,EAAE,CAAC;YACf,UAAU,EAAE,CAAC;YACb,QAAQ,EAAE,yBAAyB;SACpC,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,YAA6B;QACvD,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACjC,KAAK,CAAC,IAAI,CAAC,WAAW,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC;QAC7C,KAAK,CAAC,IAAI,CAAC,aAAa,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;QACjD,KAAK,CAAC,IAAI,CAAC,YAAY,YAAY,CAAC,OAAO,CAAC,MAAM,OAAO,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC;QACtF,KAAK,CAAC,IAAI,CAAC,gBAAgB,YAAY,CAAC,UAAU,IAAI,CAAC,CAAC;QACxD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrD,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACvC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACxC,KAAK,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YAC/B,KAAK,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YACnC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;CACF;AArRD,sCAqRC"}