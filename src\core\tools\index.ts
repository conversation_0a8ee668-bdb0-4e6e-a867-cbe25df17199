// Base tools
export {
  BaseTool,
  ToolMetadata,
  ToolValidationR<PERSON>ult,
  ToolRegistry,
  ToolExecutionContext,
  ToolExecutionResult,
  ToolFilter
} from './base';

// Filesystem tools
export { LSTool } from './filesystem/LSTool';
export { ReadFileTool } from './filesystem/ReadFileTool';
export { WriteFileTool } from './filesystem/WriteFileTool';
export { EditTool } from './filesystem/EditTool';

// Execution tools
export { ShellTool, ShellExecutionResult } from './execution/ShellTool';

// Web tools
export { WebFetchTool, WebFetchResult } from './web/WebFetchTool';
export { WebSearchTool, SearchResult, WebSearchResult } from './web/WebSearchTool';

// Memory tools
export { MemoryTool, MemoryEntry, MemorySearchResult } from './memory/MemoryTool';

// MCP tools
export { MCPTool, MCPServer, MCPResource, MCPTool as MCPToolInterface } from './mcp/MCPTool';

// Import classes for factory function
import { ToolRegistry } from './base/ToolRegistry';
import { LSTool } from './filesystem/LSTool';
import { ReadFileTool } from './filesystem/ReadFileTool';
import { WriteFileTool } from './filesystem/WriteFileTool';
import { EditTool } from './filesystem/EditTool';
import { ShellTool } from './execution/ShellTool';
import { WebFetchTool } from './web/WebFetchTool';
import { WebSearchTool } from './web/WebSearchTool';
import { MemoryTool } from './memory/MemoryTool';
import { MCPTool } from './mcp/MCPTool';

// Tool factory function
export function createDefaultToolRegistry() {
  const registry = new ToolRegistry();

  // Register filesystem tools
  registry.registerTool(new LSTool());
  registry.registerTool(new ReadFileTool());
  registry.registerTool(new WriteFileTool());
  registry.registerTool(new EditTool());

  // Register execution tools
  registry.registerTool(new ShellTool());

  // Register web tools
  registry.registerTool(new WebFetchTool());
  registry.registerTool(new WebSearchTool());

  // Register memory tools
  registry.registerTool(new MemoryTool());

  // Register MCP tools
  registry.registerTool(new MCPTool());

  return registry;
}

// Tool categories
export const TOOL_CATEGORIES = {
  FILESYSTEM: 'filesystem',
  EXECUTION: 'execution',
  WEB: 'web',
  MEMORY: 'memory',
  MCP: 'mcp',
} as const;

export type ToolCategory = typeof TOOL_CATEGORIES[keyof typeof TOOL_CATEGORIES];

// Common tool configurations
export const TOOL_CONFIGS = {
  DEFAULT_TIMEOUT: 30000,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_CONTENT_LENGTH: 100000,
  DEFAULT_ENCODING: 'utf8',
} as const;
