{"version": 3, "file": "BaseTool.js", "sourceRoot": "", "sources": ["../../../../src/core/tools/base/BaseTool.ts"], "names": [], "mappings": ";;;AAiBA,MAAsB,QAAQ;IAUlB,QAAQ,CAAe;IAEjC,YAAY,QAAsB;QAChC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAKM,KAAK,CAAC,oBAAoB,CAAC,MAA2B;QAC3D,6CAA6C;QAC7C,OAAO,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IAC9D,CAAC;IAEM,WAAW;QAChB,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAChC,CAAC;IAEM,OAAO;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IACjC,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC/B,CAAC;IAES,mBAAmB,CAC3B,OAAe,EACf,cAAuB,EACvB,QAA8B;QAE9B,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO;YACP,cAAc;YACd,QAAQ;SACT,CAAC;IACJ,CAAC;IAES,iBAAiB,CACzB,KAAa,EACb,QAA8B;QAE9B,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,EAAE;YACX,KAAK;YACL,QAAQ;SACT,CAAC;IACJ,CAAC;IAES,sBAAsB,CAC9B,MAA2B,EAC3B,cAAwB;QAExB,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;gBAChF,MAAM,CAAC,IAAI,CAAC,uBAAuB,KAAK,cAAc,CAAC,CAAC;YAC1D,CAAC;iBAAM,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC5E,MAAM,CAAC,IAAI,CAAC,uBAAuB,KAAK,mBAAmB,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAES,mBAAmB,CAC3B,KAAU,EACV,SAAiB,EACjB,OAKC;QAED,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,oBAAoB,CAAC,CAAC;YACzD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,mBAAmB,CAAC,CAAC;YACxD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,sBAAsB,OAAO,CAAC,SAAS,aAAa,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,qBAAqB,OAAO,CAAC,SAAS,aAAa,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,OAAO,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,mCAAmC,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,mBAAmB,CAC3B,KAAU,EACV,SAAiB,EACjB,OAIC;QAED,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,0BAA0B,CAAC,CAAC;YAC/D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,sBAAsB,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,OAAO,EAAE,GAAG,KAAK,SAAS,IAAI,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,sBAAsB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,OAAO,EAAE,GAAG,KAAK,SAAS,IAAI,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,qBAAqB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,oBAAoB,CAAC,KAAU,EAAE,SAAiB;QAC1D,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,CAAC,cAAc,SAAS,qBAAqB,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAES,kBAAkB,CAC1B,KAAU,EACV,SAAiB,EACjB,OAIC;QAED,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,oBAAoB,CAAC,CAAC;YACzD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,wBAAwB,OAAO,CAAC,SAAS,QAAQ,CAAC,CAAC;QACxF,CAAC;QAED,IAAI,OAAO,EAAE,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,uBAAuB,OAAO,CAAC,SAAS,QAAQ,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,QAAQ,GAAG,OAAO,IAAI,CAAC;gBAE7B,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;oBAC9E,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAClE,CAAC;qBAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC;oBAC1E,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,iBAAiB,CAAC,KAAU,EAAE,SAAiB;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC1D,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,MAAM,CAAC;QAErC,MAAM,IAAI,GAAG,KAAe,CAAC;QAE7B,oCAAoC;QACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,cAAc,SAAS,oCAAoC,CAAC,CAAC;QAC3E,CAAC;QAED,kEAAkE;QAClE,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACpD,iEAAiE;YACjE,iDAAiD;QACnD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,YAAY,CAAC,IAAY;QACjC,mCAAmC;QACnC,OAAO,IAAI;aACR,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,qCAAqC;aAC1D,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,mCAAmC;aACrD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,6BAA6B;aAClD,IAAI,EAAE,CAAC;IACZ,CAAC;IAES,cAAc,CAAC,KAAa;QACpC,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,GAAG,KAAK,CAAC;QACjB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,OAAO,IAAI,IAAI,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,IAAI,IAAI,IAAI,CAAC;YACb,SAAS,EAAE,CAAC;QACd,CAAC;QAED,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;IAClD,CAAC;IAES,eAAe,CAAC,OAAe,EAAE,YAAoB,IAAI;QACjE,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;YAChC,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IACrD,CAAC;IAEM,QAAQ;QACb,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC;IACpD,CAAC;CACF;AAnQD,4BAmQC"}