import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';
export declare class WriteFileTool extends BaseTool {
    name: string;
    description: string;
    requiresConfirmation: boolean;
    parameters: {
        type: "object";
        properties: {
            file_path: {
                type: string;
                description: string;
            };
            content: {
                type: string;
                description: string;
            };
            encoding: {
                type: string;
                enum: string[];
                description: string;
                default: string;
            };
            create_directories: {
                type: string;
                description: string;
                default: boolean;
            };
            backup_existing: {
                type: string;
                description: string;
                default: boolean;
            };
            append: {
                type: string;
                description: string;
                default: boolean;
            };
            mode: {
                type: string;
                description: string;
                pattern: string;
            };
        };
        required: string[];
    };
    constructor();
    validate(params: Record<string, any>): Promise<ToolValidationResult>;
    execute(params: Record<string, any>): Promise<ToolResult>;
    private createBackup;
    private createDisplayContent;
    shouldConfirmExecute(params: Record<string, any>): Promise<boolean>;
}
//# sourceMappingURL=WriteFileTool.d.ts.map