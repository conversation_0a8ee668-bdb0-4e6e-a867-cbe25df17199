"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIEngine = void 0;
const ProviderFactory_1 = require("../providers/ProviderFactory");
const ToolExecutionEngine_1 = require("../tools/base/ToolExecutionEngine");
class AIEngine {
    configManager;
    toolRegistry;
    toolExecutionEngine;
    providerFactory;
    toolConfirmationHandler;
    constructor(configManager, toolRegistry, toolConfirmationHandler) {
        this.configManager = configManager;
        this.toolRegistry = toolRegistry;
        this.toolExecutionEngine = new ToolExecutionEngine_1.ToolExecutionEngine(toolRegistry, toolConfirmationHandler);
        this.providerFactory = new ProviderFactory_1.ProviderFactory();
        this.toolConfirmationHandler = toolConfirmationHandler;
    }
    setToolConfirmationHandler(handler) {
        this.toolConfirmationHandler = handler;
        this.toolExecutionEngine.setConfirmationHandler(handler);
    }
    async processMessage(messages, options = {}) {
        const provider = this.getProvider(options.provider);
        const availableTools = this.getAvailableTools(options.tools);
        try {
            // Send initial message to AI provider
            const response = await provider.sendMessage(messages, availableTools, {
                model: options.model,
                temperature: options.temperature,
                maxTokens: options.maxTokens,
                stream: false
            });
            // Handle tool calls if present
            const toolResults = [];
            if (response.toolCalls && response.toolCalls.length > 0) {
                for (const toolCall of response.toolCalls) {
                    const result = await this.executeToolCall(toolCall);
                    toolResults.push(result);
                }
                // If tools were executed, send results back to AI for final response
                if (toolResults.length > 0) {
                    const toolMessages = this.createToolMessages(toolResults);
                    const finalMessages = [...messages, response, ...toolMessages];
                    const finalResponse = await provider.sendMessage(finalMessages, [], {
                        model: options.model,
                        temperature: options.temperature,
                        maxTokens: options.maxTokens,
                        stream: false
                    });
                    return {
                        message: finalResponse,
                        toolCalls: toolResults,
                        usage: finalResponse.metadata?.usage,
                        finishReason: finalResponse.metadata?.finishReason
                    };
                }
            }
            return {
                message: response,
                toolCalls: toolResults,
                usage: response.metadata?.usage,
                finishReason: response.metadata?.finishReason
            };
        }
        catch (error) {
            throw new Error(`AI Engine processing failed: ${error}`);
        }
    }
    async *streamMessage(messages, options = {}) {
        const provider = this.getProvider(options.provider);
        const availableTools = this.getAvailableTools(options.tools);
        if (!provider.streamMessage) {
            throw new Error('Provider does not support streaming');
        }
        try {
            let fullContent = '';
            const generator = provider.streamMessage(messages, availableTools, {
                model: options.model,
                temperature: options.temperature,
                maxTokens: options.maxTokens,
                stream: true
            });
            let finalMessage;
            for await (const chunk of generator) {
                if (typeof chunk === 'string') {
                    fullContent += chunk;
                    yield chunk;
                }
                else {
                    finalMessage = chunk;
                }
            }
            // Handle tool calls if present
            const toolResults = [];
            if (finalMessage.toolCalls && finalMessage.toolCalls.length > 0) {
                for (const toolCall of finalMessage.toolCalls) {
                    const result = await this.executeToolCall(toolCall);
                    toolResults.push(result);
                }
                // If tools were executed, send results back to AI for final response
                if (toolResults.length > 0) {
                    const toolMessages = this.createToolMessages(toolResults);
                    const finalMessages = [...messages, finalMessage, ...toolMessages];
                    const finalResponse = await provider.sendMessage(finalMessages, [], {
                        model: options.model,
                        temperature: options.temperature,
                        maxTokens: options.maxTokens,
                        stream: false
                    });
                    return {
                        message: finalResponse,
                        toolCalls: toolResults,
                        usage: finalResponse.metadata?.usage,
                        finishReason: finalResponse.metadata?.finishReason
                    };
                }
            }
            return {
                message: finalMessage,
                toolCalls: toolResults,
                usage: finalMessage.metadata?.usage,
                finishReason: finalMessage.metadata?.finishReason
            };
        }
        catch (error) {
            throw new Error(`AI Engine streaming failed: ${error}`);
        }
    }
    getProvider(providerName) {
        const name = providerName || this.configManager.getDefaultProvider();
        const config = this.configManager.getProviderConfigForClient(name);
        return ProviderFactory_1.ProviderFactory.createProvider(name, config);
    }
    getAvailableTools(requestedTools) {
        if (requestedTools) {
            return requestedTools;
        }
        // Get all enabled tools from registry
        const allTools = this.toolRegistry.getAllTools();
        return allTools.filter(tool => {
            const category = tool.getCategory();
            return this.configManager.isToolCategoryEnabled(category);
        });
    }
    async executeToolCall(toolCall) {
        try {
            const result = await this.toolExecutionEngine.executeSingleTool(toolCall.name, toolCall.parameters, {
                requireConfirmation: this.configManager.requiresToolConfirmation(),
                timeout: 30000,
                retries: 0
            });
            return result;
        }
        catch (error) {
            return {
                success: false,
                content: '',
                error: `Tool execution failed: ${error}`,
                toolName: toolCall.name,
                executionTime: 0,
                context: {}
            };
        }
    }
    createToolMessages(toolResults) {
        return toolResults.map(result => ({
            id: this.generateId(),
            role: 'tool',
            content: result.success ? result.content : result.error || 'Tool execution failed',
            timestamp: new Date(),
            metadata: {
                toolCallId: result.toolName,
                toolResult: result
            }
        }));
    }
    generateId() {
        return Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
    }
    // Utility methods
    async testProvider(providerName) {
        try {
            const provider = this.getProvider(providerName);
            return await provider.testConnection();
        }
        catch (error) {
            return false;
        }
    }
    async getAvailableModels(providerName) {
        try {
            const provider = this.getProvider(providerName);
            return await provider.getAvailableModels();
        }
        catch (error) {
            return [];
        }
    }
    getRegisteredTools() {
        return this.toolRegistry.getAllTools();
    }
    getToolsByCategory(category) {
        return this.toolRegistry.getToolsByCategory(category);
    }
    isToolEnabled(toolName) {
        return this.toolRegistry.isToolEnabled(toolName);
    }
    enableTool(toolName) {
        this.toolRegistry.enableTool(toolName);
    }
    disableTool(toolName) {
        this.toolRegistry.disableTool(toolName);
    }
}
exports.AIEngine = AIEngine;
//# sourceMappingURL=AIEngine.js.map