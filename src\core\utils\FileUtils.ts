import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';

export interface FileInfo {
  path: string;
  name: string;
  size: number;
  isDirectory: boolean;
  isFile: boolean;
  extension: string;
  mimeType?: string;
  createdAt: Date;
  modifiedAt: Date;
  permissions: string;
}

export interface DirectoryListing {
  path: string;
  files: FileInfo[];
  directories: FileInfo[];
  totalSize: number;
  totalFiles: number;
  totalDirectories: number;
}

export class FileUtils {
  private static readonly MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
  private static readonly SAFE_EXTENSIONS = new Set([
    '.txt', '.md', '.json', '.yaml', '.yml', '.xml', '.csv',
    '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp',
    '.h', '.hpp', '.cs', '.php', '.rb', '.go', '.rs', '.swift',
    '.html', '.css', '.scss', '.sass', '.less', '.vue', '.svelte',
    '.sql', '.sh', '.bat', '.ps1', '.dockerfile', '.gitignore',
    '.env', '.ini', '.conf', '.config', '.toml', '.lock'
  ]);

  public static async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  public static async getFileInfo(filePath: string): Promise<FileInfo> {
    const stats = await fs.stat(filePath);
    const parsedPath = path.parse(filePath);

    return {
      path: filePath,
      name: parsedPath.base,
      size: stats.size,
      isDirectory: stats.isDirectory(),
      isFile: stats.isFile(),
      extension: parsedPath.ext,
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime,
      permissions: this.getPermissionsString(stats.mode),
    };
  }

  public static async listDirectory(
    dirPath: string,
    options: {
      recursive?: boolean;
      includeHidden?: boolean;
      sortBy?: 'name' | 'size' | 'modified';
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<DirectoryListing> {
    const {
      recursive = false,
      includeHidden = false,
      sortBy = 'name',
      sortOrder = 'asc'
    } = options;

    const files: FileInfo[] = [];
    const directories: FileInfo[] = [];

    const entries = await fs.readdir(dirPath);
    
    for (const entry of entries) {
      if (!includeHidden && entry.startsWith('.')) {
        continue;
      }

      const fullPath = path.join(dirPath, entry);
      const fileInfo = await this.getFileInfo(fullPath);

      if (fileInfo.isDirectory) {
        directories.push(fileInfo);
        
        if (recursive) {
          const subListing = await this.listDirectory(fullPath, options);
          files.push(...subListing.files);
          directories.push(...subListing.directories);
        }
      } else {
        files.push(fileInfo);
      }
    }

    // Sort files and directories
    const sortFn = this.getSortFunction(sortBy, sortOrder);
    files.sort(sortFn);
    directories.sort(sortFn);

    const totalSize = files.reduce((sum, file) => sum + file.size, 0);

    return {
      path: dirPath,
      files,
      directories,
      totalSize,
      totalFiles: files.length,
      totalDirectories: directories.length,
    };
  }

  public static async readFile(
    filePath: string,
    options: {
      encoding?: BufferEncoding;
      maxSize?: number;
      startLine?: number;
      endLine?: number;
    } = {}
  ): Promise<string> {
    const {
      encoding = 'utf8',
      maxSize = this.MAX_FILE_SIZE,
      startLine,
      endLine
    } = options;

    // Check file size
    const stats = await fs.stat(filePath);
    if (stats.size > maxSize) {
      throw new Error(`File too large: ${stats.size} bytes (max: ${maxSize})`);
    }

    // Check if file extension is safe
    const ext = path.extname(filePath).toLowerCase();
    if (!this.SAFE_EXTENSIONS.has(ext)) {
      throw new Error(`Unsafe file extension: ${ext}`);
    }

    let content = await fs.readFile(filePath, encoding);

    // Handle line range if specified
    if (startLine !== undefined || endLine !== undefined) {
      const lines = content.split('\n');
      const start = Math.max(0, (startLine || 1) - 1);
      const end = endLine ? Math.min(lines.length, endLine) : lines.length;
      content = lines.slice(start, end).join('\n');
    }

    return content;
  }

  public static async writeFile(
    filePath: string,
    content: string,
    options: {
      encoding?: BufferEncoding;
      createBackup?: boolean;
      ensureDir?: boolean;
      mode?: number;
    } = {}
  ): Promise<void> {
    const {
      encoding = 'utf8',
      createBackup = false,
      ensureDir = true,
      mode
    } = options;

    // Ensure directory exists
    if (ensureDir) {
      await fs.ensureDir(path.dirname(filePath));
    }

    // Create backup if requested
    if (createBackup && await this.exists(filePath)) {
      const backupPath = `${filePath}.backup.${Date.now()}`;
      await fs.copy(filePath, backupPath);
    }

    // Write file
    await fs.writeFile(filePath, content, { encoding, mode });
  }

  public static async appendFile(
    filePath: string,
    content: string,
    options: {
      encoding?: BufferEncoding;
      ensureDir?: boolean;
    } = {}
  ): Promise<void> {
    const { encoding = 'utf8', ensureDir = true } = options;

    if (ensureDir) {
      await fs.ensureDir(path.dirname(filePath));
    }

    await fs.appendFile(filePath, content, encoding);
  }

  public static async copyFile(
    sourcePath: string,
    destPath: string,
    options: {
      overwrite?: boolean;
      preserveTimestamps?: boolean;
      ensureDir?: boolean;
    } = {}
  ): Promise<void> {
    const {
      overwrite = false,
      preserveTimestamps = true,
      ensureDir = true
    } = options;

    if (!overwrite && await this.exists(destPath)) {
      throw new Error(`Destination file already exists: ${destPath}`);
    }

    if (ensureDir) {
      await fs.ensureDir(path.dirname(destPath));
    }

    await fs.copy(sourcePath, destPath, {
      overwrite,
      preserveTimestamps,
    });
  }

  public static async moveFile(
    sourcePath: string,
    destPath: string,
    options: {
      overwrite?: boolean;
      ensureDir?: boolean;
    } = {}
  ): Promise<void> {
    const { overwrite = false, ensureDir = true } = options;

    if (!overwrite && await this.exists(destPath)) {
      throw new Error(`Destination file already exists: ${destPath}`);
    }

    if (ensureDir) {
      await fs.ensureDir(path.dirname(destPath));
    }

    await fs.move(sourcePath, destPath, { overwrite });
  }

  public static async deleteFile(filePath: string): Promise<void> {
    await fs.remove(filePath);
  }

  public static async createDirectory(
    dirPath: string,
    options: {
      recursive?: boolean;
      mode?: number;
    } = {}
  ): Promise<void> {
    const { recursive = true, mode } = options;

    if (recursive) {
      await fs.ensureDir(dirPath);
      if (mode) {
        await fs.chmod(dirPath, mode);
      }
    } else {
      await fs.mkdir(dirPath, { mode });
    }
  }

  public static async getFileHash(
    filePath: string,
    algorithm: string = 'sha256'
  ): Promise<string> {
    const hash = crypto.createHash(algorithm);
    const stream = fs.createReadStream(filePath);

    return new Promise((resolve, reject) => {
      stream.on('data', (data) => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }

  public static async findFiles(
    searchPath: string,
    pattern: string | RegExp,
    options: {
      recursive?: boolean;
      includeDirectories?: boolean;
      maxDepth?: number;
    } = {}
  ): Promise<string[]> {
    const {
      recursive = true,
      includeDirectories = false,
      maxDepth = Infinity
    } = options;

    const results: string[] = [];
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;

    const search = async (currentPath: string, depth: number = 0): Promise<void> => {
      if (depth > maxDepth) return;

      const entries = await fs.readdir(currentPath);

      for (const entry of entries) {
        const fullPath = path.join(currentPath, entry);
        const stats = await fs.stat(fullPath);

        if (stats.isDirectory()) {
          if (includeDirectories && regex.test(entry)) {
            results.push(fullPath);
          }
          if (recursive) {
            await search(fullPath, depth + 1);
          }
        } else if (regex.test(entry)) {
          results.push(fullPath);
        }
      }
    };

    await search(searchPath);
    return results;
  }

  public static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
  }

  public static isTextFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return this.SAFE_EXTENSIONS.has(ext);
  }

  public static sanitizePath(inputPath: string): string {
    // Remove dangerous path components
    return path.normalize(inputPath)
      .replace(/\.\./g, '')
      .replace(/[<>:"|?*]/g, '');
  }

  public static getRelativePath(from: string, to: string): string {
    return path.relative(from, to);
  }

  public static joinPath(...segments: string[]): string {
    return path.join(...segments);
  }

  public static resolvePath(...segments: string[]): string {
    return path.resolve(...segments);
  }

  private static getPermissionsString(mode: number): string {
    const permissions: string[] = [];
    
    // Owner permissions
    permissions.push((mode & 0o400) ? 'r' : '-');
    permissions.push((mode & 0o200) ? 'w' : '-');
    permissions.push((mode & 0o100) ? 'x' : '-');
    
    // Group permissions
    permissions.push((mode & 0o040) ? 'r' : '-');
    permissions.push((mode & 0o020) ? 'w' : '-');
    permissions.push((mode & 0o010) ? 'x' : '-');
    
    // Other permissions
    permissions.push((mode & 0o004) ? 'r' : '-');
    permissions.push((mode & 0o002) ? 'w' : '-');
    permissions.push((mode & 0o001) ? 'x' : '-');
    
    return permissions.join('');
  }

  private static getSortFunction(
    sortBy: 'name' | 'size' | 'modified',
    sortOrder: 'asc' | 'desc'
  ): (a: FileInfo, b: FileInfo) => number {
    const multiplier = sortOrder === 'asc' ? 1 : -1;

    switch (sortBy) {
      case 'name':
        return (a, b) => a.name.localeCompare(b.name) * multiplier;
      case 'size':
        return (a, b) => (a.size - b.size) * multiplier;
      case 'modified':
        return (a, b) => (a.modifiedAt.getTime() - b.modifiedAt.getTime()) * multiplier;
      default:
        return (a, b) => a.name.localeCompare(b.name) * multiplier;
    }
  }
}
