{"version": 3, "file": "AIEngine.js", "sourceRoot": "", "sources": ["../../../src/core/engine/AIEngine.ts"], "names": [], "mappings": ";;;AACA,kEAA+D;AAE/D,2EAAwE;AA6BxE,MAAa,QAAQ;IACX,aAAa,CAAgB;IAC7B,YAAY,CAAe;IAC3B,mBAAmB,CAAsB;IACzC,eAAe,CAAkB;IACjC,uBAAuB,CAA2B;IAE1D,YACE,aAA4B,EAC5B,YAA0B,EAC1B,uBAAiD;QAEjD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,YAAY,EAAE,uBAAuB,CAAC,CAAC;QAC1F,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;IACzD,CAAC;IAEM,0BAA0B,CAAC,OAAgC;QAChE,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC;QACvC,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,QAAuB,EACvB,UAA2B,EAAE;QAE7B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE7D,IAAI,CAAC;YACH,sCAAsC;YACtC,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,EAAE;gBACpE,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,WAAW,GAA0B,EAAE,CAAC;YAC9C,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxD,KAAK,MAAM,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;oBAC1C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBACpD,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,CAAC;gBAED,qEAAqE;gBACrE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;oBAC1D,MAAM,aAAa,GAAG,CAAC,GAAG,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,CAAC;oBAE/D,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE;wBAClE,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;wBAC5B,MAAM,EAAE,KAAK;qBACd,CAAC,CAAC;oBAEH,OAAO;wBACL,OAAO,EAAE,aAAa;wBACtB,SAAS,EAAE,WAAW;wBACtB,KAAK,EAAE,aAAa,CAAC,QAAQ,EAAE,KAAK;wBACpC,YAAY,EAAE,aAAa,CAAC,QAAQ,EAAE,YAAY;qBACnD,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,QAAQ;gBACjB,SAAS,EAAE,WAAW;gBACtB,KAAK,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK;gBAC/B,YAAY,EAAE,QAAQ,CAAC,QAAQ,EAAE,YAAY;aAC9C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,CAAC,aAAa,CACzB,QAAuB,EACvB,UAA2B,EAAE;QAE7B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE7D,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC;YACH,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,cAAc,EAAE;gBACjE,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,YAAyB,CAAC;YAE9B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;gBACpC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;oBAC9B,WAAW,IAAI,KAAK,CAAC;oBACrB,MAAM,KAAK,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,YAAY,GAAG,KAAK,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,+BAA+B;YAC/B,MAAM,WAAW,GAA0B,EAAE,CAAC;YAC9C,IAAI,YAAa,CAAC,SAAS,IAAI,YAAa,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,KAAK,MAAM,QAAQ,IAAI,YAAa,CAAC,SAAS,EAAE,CAAC;oBAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBACpD,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,CAAC;gBAED,qEAAqE;gBACrE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;oBAC1D,MAAM,aAAa,GAAG,CAAC,GAAG,QAAQ,EAAE,YAAa,EAAE,GAAG,YAAY,CAAC,CAAC;oBAEpE,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE;wBAClE,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,SAAS,EAAE,OAAO,CAAC,SAAS;wBAC5B,MAAM,EAAE,KAAK;qBACd,CAAC,CAAC;oBAEH,OAAO;wBACL,OAAO,EAAE,aAAa;wBACtB,SAAS,EAAE,WAAW;wBACtB,KAAK,EAAE,aAAa,CAAC,QAAQ,EAAE,KAAK;wBACpC,YAAY,EAAE,aAAa,CAAC,QAAQ,EAAE,YAAY;qBACnD,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,YAAa;gBACtB,SAAS,EAAE,WAAW;gBACtB,KAAK,EAAE,YAAa,CAAC,QAAQ,EAAE,KAAK;gBACpC,YAAY,EAAE,YAAa,CAAC,QAAQ,EAAE,YAAY;aACnD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,YAAqB;QACvC,MAAM,IAAI,GAAG,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QACnE,OAAO,iCAAe,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACtD,CAAC;IAEO,iBAAiB,CAAC,cAAsB;QAC9C,IAAI,cAAc,EAAE,CAAC;YACnB,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,sCAAsC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAa;QACzC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAC7D,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,UAAU,EACnB;gBACE,mBAAmB,EAAE,IAAI,CAAC,aAAa,CAAC,wBAAwB,EAAE;gBAClE,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,CAAC;aACX,CACF,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,0BAA0B,KAAK,EAAE;gBACxC,QAAQ,EAAE,QAAQ,CAAC,IAAI;gBACvB,aAAa,EAAE,CAAC;gBAChB,OAAO,EAAE,EAAE;aACZ,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,WAAkC;QAC3D,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChC,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI,EAAE,MAAe;YACrB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,uBAAuB;YAClF,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE;gBACR,UAAU,EAAE,MAAM,CAAC,QAAQ;gBAC3B,UAAU,EAAE,MAAM;aACnB;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,UAAU;QAChB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,kBAAkB;IACX,KAAK,CAAC,YAAY,CAAC,YAAqB;QAC7C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAChD,OAAO,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,YAAqB;QACnD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAChD,OAAO,MAAM,QAAQ,CAAC,kBAAkB,EAAE,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEM,kBAAkB;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IACzC,CAAC;IAEM,kBAAkB,CAAC,QAAgB;QACxC,OAAO,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IAEM,aAAa,CAAC,QAAgB;QACnC,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAEM,UAAU,CAAC,QAAgB;QAChC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAEM,WAAW,CAAC,QAAgB;QACjC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;CACF;AA3PD,4BA2PC"}