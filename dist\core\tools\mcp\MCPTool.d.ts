import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';
export interface MCPServer {
    name: string;
    command: string;
    args: string[];
    env?: Record<string, string>;
    cwd?: string;
}
export interface MCPResource {
    uri: string;
    name: string;
    description?: string;
    mimeType?: string;
}
export interface MCPTool {
    name: string;
    description: string;
    inputSchema: any;
}
export declare class MCPTool extends BaseTool {
    name: string;
    description: string;
    requiresConfirmation: boolean;
    parameters: {
        type: "object";
        properties: {
            action: {
                type: string;
                enum: string[];
                description: string;
            };
            server_name: {
                type: string;
                description: string;
            };
            server_config: {
                type: string;
                description: string;
                properties: {
                    command: {
                        type: string;
                    };
                    args: {
                        type: string;
                        items: {
                            type: string;
                        };
                    };
                    env: {
                        type: string;
                        additionalProperties: {
                            type: string;
                        };
                    };
                    cwd: {
                        type: string;
                    };
                };
            };
            resource_uri: {
                type: string;
                description: string;
            };
            tool_name: {
                type: string;
                description: string;
            };
            tool_arguments: {
                type: string;
                description: string;
            };
        };
        required: string[];
    };
    private connectedServers;
    constructor();
    validate(params: Record<string, any>): Promise<ToolValidationResult>;
    execute(params: Record<string, any>): Promise<ToolResult>;
    private listServers;
    private connectServer;
    private disconnectServer;
    private listResources;
    private readResource;
    private listTools;
    private callTool;
    shouldConfirmExecute(params: Record<string, any>): Promise<boolean>;
}
//# sourceMappingURL=MCPTool.d.ts.map