import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';

export interface MCPServer {
  name: string;
  command: string;
  args: string[];
  env?: Record<string, string>;
  cwd?: string;
}

export interface MCPResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: any;
}

export class MCPTool extends BaseTool {
  public name = 'mcp';
  public description = 'Interacts with Model Context Protocol (MCP) servers for extended functionality';
  public requiresConfirmation = true;

  public parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['list_servers', 'connect', 'disconnect', 'list_resources', 'read_resource', 'list_tools', 'call_tool'],
        description: 'MCP action to perform',
      },
      server_name: {
        type: 'string',
        description: 'Name of the MCP server (required for most actions)',
      },
      server_config: {
        type: 'object',
        description: 'Server configuration for connect action',
        properties: {
          command: { type: 'string' },
          args: { type: 'array', items: { type: 'string' } },
          env: { type: 'object', additionalProperties: { type: 'string' } },
          cwd: { type: 'string' },
        },
      },
      resource_uri: {
        type: 'string',
        description: 'URI of the resource to read',
      },
      tool_name: {
        type: 'string',
        description: 'Name of the tool to call',
      },
      tool_arguments: {
        type: 'object',
        description: 'Arguments to pass to the tool',
      },
    },
    required: ['action'],
  };

  private connectedServers: Map<string, MCPServerConnection> = new Map();

  constructor() {
    super({
      category: 'mcp',
      tags: ['mcp', 'protocol', 'extension'],
      version: '1.0.0',
      dangerous: true,
      requiresConfirmation: true,
    });
  }

  public async validate(params: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required parameters
    const requiredValidation = this.validateRequiredParams(params, ['action']);
    if (!requiredValidation.valid) {
      return requiredValidation;
    }

    const action = params.action;
    const validActions = ['list_servers', 'connect', 'disconnect', 'list_resources', 'read_resource', 'list_tools', 'call_tool'];
    
    if (!validActions.includes(action)) {
      errors.push(`Action must be one of: ${validActions.join(', ')}`);
    }

    // Validate action-specific requirements
    switch (action) {
      case 'connect':
        if (!params.server_name) {
          errors.push('Server name is required for connect action');
        }
        if (!params.server_config) {
          errors.push('Server config is required for connect action');
        } else {
          if (!params.server_config.command) {
            errors.push('Server command is required in server config');
          }
        }
        break;
      case 'disconnect':
      case 'list_resources':
      case 'list_tools':
        if (!params.server_name) {
          errors.push(`Server name is required for ${action} action`);
        }
        break;
      case 'read_resource':
        if (!params.server_name) {
          errors.push('Server name is required for read_resource action');
        }
        if (!params.resource_uri) {
          errors.push('Resource URI is required for read_resource action');
        }
        break;
      case 'call_tool':
        if (!params.server_name) {
          errors.push('Server name is required for call_tool action');
        }
        if (!params.tool_name) {
          errors.push('Tool name is required for call_tool action');
        }
        break;
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  public async execute(params: Record<string, any>): Promise<ToolResult> {
    try {
      const { action } = params;

      switch (action) {
        case 'list_servers':
          return await this.listServers();
        case 'connect':
          return await this.connectServer(params);
        case 'disconnect':
          return await this.disconnectServer(params);
        case 'list_resources':
          return await this.listResources(params);
        case 'read_resource':
          return await this.readResource(params);
        case 'list_tools':
          return await this.listTools(params);
        case 'call_tool':
          return await this.callTool(params);
        default:
          return this.createErrorResult(`Unknown MCP action: ${action}`);
      }
    } catch (error) {
      return this.createErrorResult(
        `MCP operation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async listServers(): Promise<ToolResult> {
    const servers = Array.from(this.connectedServers.keys());
    
    const formatted = servers.length > 0 
      ? `Connected MCP Servers:\n${servers.map(name => `- ${name}`).join('\n')}`
      : 'No MCP servers currently connected';

    return this.createSuccessResult(
      JSON.stringify(servers),
      formatted,
      { action: 'list_servers', serverCount: servers.length }
    );
  }

  private async connectServer(params: Record<string, any>): Promise<ToolResult> {
    const { server_name, server_config } = params;

    if (this.connectedServers.has(server_name)) {
      return this.createErrorResult(`Server ${server_name} is already connected`);
    }

    // Create mock connection for demonstration
    // In a real implementation, this would establish an actual MCP connection
    const connection: MCPServerConnection = {
      name: server_name,
      config: server_config,
      connected: true,
      resources: [],
      tools: [],
    };

    this.connectedServers.set(server_name, connection);

    return this.createSuccessResult(
      JSON.stringify(connection),
      `Successfully connected to MCP server: ${server_name}`,
      { action: 'connect', serverName: server_name }
    );
  }

  private async disconnectServer(params: Record<string, any>): Promise<ToolResult> {
    const { server_name } = params;

    if (!this.connectedServers.has(server_name)) {
      return this.createErrorResult(`Server ${server_name} is not connected`);
    }

    this.connectedServers.delete(server_name);

    return this.createSuccessResult(
      `{"disconnected": "${server_name}"}`,
      `Successfully disconnected from MCP server: ${server_name}`,
      { action: 'disconnect', serverName: server_name }
    );
  }

  private async listResources(params: Record<string, any>): Promise<ToolResult> {
    const { server_name } = params;
    const connection = this.connectedServers.get(server_name);

    if (!connection) {
      return this.createErrorResult(`Server ${server_name} is not connected`);
    }

    // Mock resources for demonstration
    const resources: MCPResource[] = [
      {
        uri: 'file:///example.txt',
        name: 'Example File',
        description: 'An example text file',
        mimeType: 'text/plain',
      },
    ];

    const formatted = resources.length > 0
      ? `Resources from ${server_name}:\n${resources.map(r => `- ${r.name} (${r.uri})`).join('\n')}`
      : `No resources available from ${server_name}`;

    return this.createSuccessResult(
      JSON.stringify(resources),
      formatted,
      { action: 'list_resources', serverName: server_name, resourceCount: resources.length }
    );
  }

  private async readResource(params: Record<string, any>): Promise<ToolResult> {
    const { server_name, resource_uri } = params;
    const connection = this.connectedServers.get(server_name);

    if (!connection) {
      return this.createErrorResult(`Server ${server_name} is not connected`);
    }

    // Mock resource content for demonstration
    const content = `Mock content for resource: ${resource_uri}`;

    return this.createSuccessResult(
      content,
      `Resource Content from ${server_name}:\n${resource_uri}\n${'─'.repeat(40)}\n${content}`,
      { action: 'read_resource', serverName: server_name, resourceUri: resource_uri }
    );
  }

  private async listTools(params: Record<string, any>): Promise<ToolResult> {
    const { server_name } = params;
    const connection = this.connectedServers.get(server_name);

    if (!connection) {
      return this.createErrorResult(`Server ${server_name} is not connected`);
    }

    // Mock tools for demonstration
    const tools: Array<{name: string, description: string}> = [
      {
        name: 'example_tool',
        description: 'An example MCP tool',
      },
    ];

    const formatted = tools.length > 0
      ? `Tools from ${server_name}:\n${tools.map(t => `- ${t.name}: ${t.description}`).join('\n')}`
      : `No tools available from ${server_name}`;

    return this.createSuccessResult(
      JSON.stringify(tools),
      formatted,
      { action: 'list_tools', serverName: server_name, toolCount: tools.length }
    );
  }

  private async callTool(params: Record<string, any>): Promise<ToolResult> {
    const { server_name, tool_name, tool_arguments = {} } = params;
    const connection = this.connectedServers.get(server_name);

    if (!connection) {
      return this.createErrorResult(`Server ${server_name} is not connected`);
    }

    // Mock tool execution for demonstration
    const result = {
      toolName: tool_name,
      arguments: tool_arguments,
      result: `Mock result from ${tool_name} on ${server_name}`,
      success: true,
    };

    return this.createSuccessResult(
      JSON.stringify(result),
      `Tool Execution Result:\nServer: ${server_name}\nTool: ${tool_name}\nResult: ${result.result}`,
      { action: 'call_tool', serverName: server_name, toolName: tool_name }
    );
  }

  public async shouldConfirmExecute(params: Record<string, any>): Promise<boolean> {
    // Always require confirmation for MCP operations as they can be dangerous
    const action = params.action;
    
    // Some read-only operations might not need confirmation
    const safeActions = ['list_servers', 'list_resources', 'list_tools'];
    if (safeActions.includes(action)) {
      return false;
    }

    return true;
  }
}

interface MCPServerConnection {
  name: string;
  config: any;
  connected: boolean;
  resources: MCPResource[];
  tools: MCPTool[];
}
