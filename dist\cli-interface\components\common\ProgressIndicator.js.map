{"version": 3, "file": "ProgressIndicator.js", "sourceRoot": "", "sources": ["../../../../src/cli-interface/components/common/ProgressIndicator.ts"], "names": [], "mappings": ";;;;;;AAAA,8CAA+B;AAC/B,kDAA0B;AAG1B,MAAa,oBAAoB;IACvB,OAAO,GAAe,IAAI,CAAC;IAC3B,KAAK,CAAW;IAExB,YAAY,KAAe;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAEM,KAAK,CAAC,OAAe;QAC1B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAA,aAAG,EAAC;YACjB,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAc;SACjC,CAAC,CAAC,KAAK,EAAE,CAAC;IACb,CAAC;IAEM,MAAM,CAAC,OAAe;QAC3B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC;QAC9B,CAAC;IACH,CAAC;IAEM,OAAO,CAAC,OAAgB;QAC7B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;IACH,CAAC;IAEM,IAAI,CAAC,OAAgB;QAC1B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;IACH,CAAC;IAEM,IAAI;QACT,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;IACH,CAAC;CACF;AA5CD,oDA4CC;AAED,MAAa,uBAAuB;IAC1B,KAAK,CAAW;IAExB,YAAY,KAAe;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAEM,KAAK,CAAC,OAAe;QAC1B,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,OAAe;QAC3B,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEM,OAAO,CAAC,OAAgB;QAC7B,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAEM,IAAI,CAAC,OAAgB;QAC1B,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAEM,IAAI;QACT,6BAA6B;IAC/B,CAAC;CACF;AA9BD,0DA8BC"}