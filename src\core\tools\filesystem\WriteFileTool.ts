import * as fs from 'fs-extra';
import * as path from 'path';
import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';

export class WriteFileTool extends BaseTool {
  public name = 'write_file';
  public description = 'Creates new files or overwrites existing ones with specified content';
  public requiresConfirmation = true;

  public parameters = {
    type: 'object' as const,
    properties: {
      file_path: {
        type: 'string',
        description: 'Path where the file should be written (absolute or relative)',
      },
      content: {
        type: 'string',
        description: 'Content to write to the file',
      },
      encoding: {
        type: 'string',
        enum: ['utf8', 'ascii', 'base64', 'hex'],
        description: 'Encoding to use when writing the file',
        default: 'utf8',
      },
      create_directories: {
        type: 'boolean',
        description: 'Whether to create parent directories if they don\'t exist',
        default: true,
      },
      backup_existing: {
        type: 'boolean',
        description: 'Whether to create a backup of existing file before overwriting',
        default: false,
      },
      append: {
        type: 'boolean',
        description: 'Whether to append to existing file instead of overwriting',
        default: false,
      },
      mode: {
        type: 'string',
        description: 'File permissions in octal format (e.g., "644", "755")',
        pattern: '^[0-7]{3,4}$',
      },
    },
    required: ['file_path', 'content'],
  };

  constructor() {
    super({
      category: 'filesystem',
      tags: ['file', 'write', 'create'],
      version: '1.0.0',
      dangerous: true,
      requiresConfirmation: true,
    });
  }

  public async validate(params: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required parameters
    const requiredValidation = this.validateRequiredParams(params, ['file_path', 'content']);
    if (!requiredValidation.valid) {
      return requiredValidation;
    }

    // Validate file path
    const pathErrors = this.validateStringParam(params.file_path, 'file_path');
    errors.push(...pathErrors);

    // Validate content
    const contentErrors = this.validateStringParam(params.content, 'content', {
      allowEmpty: true, // Allow empty content
    });
    errors.push(...contentErrors);

    // Validate optional parameters
    if (params.encoding !== undefined) {
      const validEncodings = ['utf8', 'ascii', 'base64', 'hex'];
      if (!validEncodings.includes(params.encoding)) {
        errors.push(`Parameter 'encoding' must be one of: ${validEncodings.join(', ')}`);
      }
    }

    if (params.create_directories !== undefined) {
      errors.push(...this.validateBooleanParam(params.create_directories, 'create_directories'));
    }

    if (params.backup_existing !== undefined) {
      errors.push(...this.validateBooleanParam(params.backup_existing, 'backup_existing'));
    }

    if (params.append !== undefined) {
      errors.push(...this.validateBooleanParam(params.append, 'append'));
    }

    if (params.mode !== undefined) {
      if (!/^[0-7]{3,4}$/.test(params.mode)) {
        errors.push('Parameter mode must be a valid octal permission string (e.g., "644", "755")');
      }
    }

    // Check path safety and permissions
    try {
      const resolvedPath = path.resolve(params.file_path);
      const dirname = path.dirname(resolvedPath);

      // Check if parent directory exists or can be created
      if (!await fs.pathExists(dirname)) {
        if (!params.create_directories) {
          errors.push(`Parent directory '${dirname}' does not exist and create_directories is false`);
        } else {
          // Check if we can create the directory
          try {
            await fs.access(path.dirname(dirname), fs.constants.W_OK);
          } catch {
            errors.push(`Cannot create parent directory '${dirname}' - permission denied`);
          }
        }
      } else {
        // Check write permission to parent directory
        try {
          await fs.access(dirname, fs.constants.W_OK);
        } catch {
          errors.push(`No write permission to directory '${dirname}'`);
        }
      }

      // Check if file already exists
      if (await fs.pathExists(resolvedPath)) {
        const stats = await fs.stat(resolvedPath);
        
        if (stats.isDirectory()) {
          errors.push(`Path '${params.file_path}' is a directory, not a file`);
        } else {
          if (!params.append && !params.backup_existing) {
            warnings.push(`File '${params.file_path}' already exists and will be overwritten`);
          }
          
          // Check if file is writable
          try {
            await fs.access(resolvedPath, fs.constants.W_OK);
          } catch {
            errors.push(`File '${params.file_path}' exists but is not writable`);
          }
        }
      }

      // Warn about large content
      if (params.content.length > 1024 * 1024) { // 1MB
        warnings.push(`Content is large (${this.formatFileSize(params.content.length)}). Consider using streaming for very large files.`);
      }

    } catch (error) {
      errors.push(`Path validation failed: ${error}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  public async execute(params: Record<string, any>): Promise<ToolResult> {
    try {
      const {
        file_path,
        content,
        encoding = 'utf8',
        create_directories = true,
        backup_existing = false,
        append = false,
        mode,
      } = params;

      const resolvedPath = path.resolve(file_path);
      const dirname = path.dirname(resolvedPath);

      // Create parent directories if needed
      if (create_directories) {
        await fs.ensureDir(dirname);
      }

      // Create backup if requested and file exists
      let backupPath: string | undefined;
      if (backup_existing && await fs.pathExists(resolvedPath)) {
        backupPath = await this.createBackup(resolvedPath);
      }

      // Write the file
      const writeOptions: any = { encoding };
      
      if (append) {
        await fs.appendFile(resolvedPath, content, writeOptions);
      } else {
        await fs.writeFile(resolvedPath, content, writeOptions);
      }

      // Set file permissions if specified
      if (mode) {
        await fs.chmod(resolvedPath, parseInt(mode, 8));
      }

      // Get file stats for metadata
      const stats = await fs.stat(resolvedPath);
      
      const metadata = {
        path: resolvedPath,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        encoding,
        append,
        backupPath,
        contentLength: content.length,
      };

      const displayContent = this.createDisplayContent(
        file_path,
        content,
        append,
        backupPath
      );

      return this.createSuccessResult(
        `File ${append ? 'appended to' : 'written'} successfully: ${file_path}`,
        displayContent,
        metadata
      );
    } catch (error) {
      return this.createErrorResult(
        `Failed to write file: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async createBackup(filePath: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    
    await fs.copy(filePath, backupPath);
    return backupPath;
  }

  private createDisplayContent(
    filePath: string,
    content: string,
    append: boolean,
    backupPath?: string
  ): string {
    const lines = content.split('\n');
    const lineCount = lines.length;
    const size = this.formatFileSize(content.length);
    
    let header = `${append ? 'Appended to' : 'Written'} file: ${filePath}`;
    header += `\nContent: ${size}, ${lineCount} lines`;
    
    if (backupPath) {
      header += `\nBackup created: ${backupPath}`;
    }
    
    const separator = '─'.repeat(Math.min(80, header.split('\n')[0].length));
    
    // Show preview of content (first and last few lines for large content)
    let contentPreview = content;
    if (lines.length > 20) {
      const firstLines = lines.slice(0, 10).join('\n');
      const lastLines = lines.slice(-10).join('\n');
      contentPreview = `${firstLines}\n\n... (${lines.length - 20} more lines) ...\n\n${lastLines}`;
    } else {
      contentPreview = this.truncateContent(content, 2000);
    }
    
    return `${header}\n${separator}\n${contentPreview}`;
  }

  public async shouldConfirmExecute(params: Record<string, any>): Promise<boolean> {
    // Always require confirmation for write operations
    const resolvedPath = path.resolve(params.file_path);
    
    // Extra confirmation for system files or important directories
    const dangerousPaths = [
      '/etc',
      '/bin',
      '/sbin',
      '/usr/bin',
      '/usr/sbin',
      '/System',
      'C:\\Windows',
      'C:\\Program Files',
    ];
    
    for (const dangerousPath of dangerousPaths) {
      if (resolvedPath.startsWith(dangerousPath)) {
        return true; // Definitely require confirmation
      }
    }
    
    // Check if overwriting existing file
    if (await fs.pathExists(resolvedPath) && !params.append) {
      return true;
    }
    
    return this.requiresConfirmation;
  }
}
